#!/usr/bin/env python3
import subprocess
import os
import sys

def run_command(command, description):
    """Ejecuta un comando y maneja errores"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd="/Users/<USER>/Desktop/emma-studio--main",
            capture_output=True, 
            text=True,
            timeout=60
        )
        
        print(f"📤 Comando: {command}")
        print(f"🔢 Código de salida: {result.returncode}")
        
        if result.stdout.strip():
            print(f"📄 Salida: {result.stdout.strip()}")
        
        if result.stderr.strip():
            print(f"⚠️ Error/Warning: {result.stderr.strip()}")
        
        if result.returncode == 0:
            print(f"✅ {description} completado exitosamente")
            return True
        else:
            print(f"❌ Error en {description}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ Timeout en {description}")
        return False
    except Exception as e:
        print(f"💥 Excepción en {description}: {e}")
        return False

def main():
    print("🚀 Ejecutando comandos Git para 'Imagen Infografía'...")
    print("=" * 60)
    
    # Verificar que estamos en el directorio correcto
    project_dir = "/Users/<USER>/Desktop/emma-studio--main"
    if not os.path.exists(project_dir):
        print(f"❌ El directorio {project_dir} no existe")
        sys.exit(1)
    
    print(f"📁 Directorio del proyecto: {project_dir}")
    
    # 1. Verificar estado
    print("\n🔍 VERIFICANDO ESTADO ACTUAL:")
    run_command("git status --porcelain", "Verificando archivos modificados")
    
    # 2. Agregar cambios
    print("\n📦 AGREGANDO CAMBIOS:")
    if not run_command("git add .", "Agregando todos los cambios"):
        print("❌ Falló al agregar cambios")
        sys.exit(1)
    
    # 3. Verificar qué se agregó
    run_command("git status --porcelain", "Verificando archivos agregados")
    
    # 4. Realizar commit
    print("\n💾 REALIZANDO COMMIT:")
    commit_cmd = 'git commit -m "Imagen Infografía" -m "literal la imagen"'
    commit_success = run_command(commit_cmd, "Realizando commit")
    
    if not commit_success:
        print("⚠️ Commit falló (posiblemente no hay cambios nuevos)")
        # Continuar con pull y push de todas formas
    
    # 5. Realizar pull
    print("\n⬇️ REALIZANDO PULL:")
    if not run_command("git pull origin main", "Realizando pull"):
        print("❌ Falló el pull")
        sys.exit(1)
    
    # 6. Realizar push
    print("\n⬆️ REALIZANDO PUSH:")
    if not run_command("git push origin main", "Realizando push"):
        print("❌ Falló el push")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 ¡Todas las operaciones Git completadas exitosamente!")
    print("📋 Resumen:")
    print("   ✅ Cambios agregados")
    print("   ✅ Commit: 'Imagen Infografía'")
    print("   ✅ Pull desde origin/main")
    print("   ✅ Push a origin/main")
    print("=" * 60)

if __name__ == "__main__":
    main()
