/**
 * Service for image generation using OpenAI's gpt-image-1 model.
 * Supports initial generation, references, and mask-based editing.
 */

import { ensureFullImageUrl } from "./detect-deployment";

// Types for OpenAI image operations
export interface OpenAIImageGenerationOptions {
  prompt: string;
  size?: "1024x1024" | "1536x1024" | "1024x1536";
}

export interface OpenAIImageReferenceOptions {
  prompt: string;
  referenceImages: File[];
  size?: "1024x1024" | "1536x1024" | "1024x1536";
}

export interface OpenAIImageMaskOptions {
  prompt: string;
  image: File;
  mask: File;
}

export interface OpenAIImageResponse {
  success: boolean;
  images?: string[];
  image_url?: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

const API_BASE_URL = "/api/v1/openai-images";

/**
 * Generate an initial image using OpenAI gpt-image-1
 */
export async function generateOpenAIImage(options: OpenAIImageGenerationOptions): Promise<OpenAIImageResponse> {
  try {
    // Create the enhanced prompt for image generation
    const enhancedPrompt = createImagePrompt(options.prompt);

    const formData = new FormData();
    formData.append("prompt", enhancedPrompt);
    formData.append("size", options.size || "1024x1024");

    const response = await fetch(`${API_BASE_URL}/generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error generating OpenAI image:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Generate image using reference images
 */
export async function editWithReferences(options: OpenAIImageReferenceOptions): Promise<OpenAIImageResponse> {
  try {
    if (options.referenceImages.length > 4) {
      return {
        success: false,
        error: "Maximum 4 reference images allowed",
      };
    }

    // Create the enhanced prompt for image generation
    const enhancedPrompt = createImagePrompt(options.prompt);

    const formData = new FormData();
    formData.append("prompt", enhancedPrompt);
    formData.append("size", options.size || "1024x1024");

    options.referenceImages.forEach((image, index) => {
      formData.append("reference_images", image);
    });

    const response = await fetch(`${API_BASE_URL}/edit-with-references`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error generating image with references:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Edit image using mask
 */
export async function editWithMask(options: OpenAIImageMaskOptions): Promise<OpenAIImageResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("image", options.image);
    formData.append("mask", options.mask);

    const response = await fetch(`${API_BASE_URL}/edit-with-mask`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error editing image with mask:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: "Tipo de archivo no válido. Use JPEG, PNG o WebP.",
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: "El archivo es demasiado grande. Máximo 10MB.",
    };
  }

  return { valid: true };
}

/**
 * Create enhanced prompt for image generation
 */
function createImagePrompt(userPrompt: string): string {
  const baseTemplate = `Create a high-quality, professional image with excellent composition, lighting, and detail. The image should be visually appealing, well-balanced, and suitable for professional use.

User request: ${userPrompt}

Generate a professional image that incorporates the user's specifications while maintaining high artistic quality and visual appeal.`;

  return baseTemplate;
}

/**
 * Multi-turn edit functionality
 */
export async function multiTurnEdit(previousResponseId: string, editPrompt: string): Promise<OpenAIImageResponse> {
  try {
    const formData = new FormData();
    formData.append("previous_response_id", previousResponseId);
    formData.append("edit_prompt", editPrompt);

    const response = await fetch(`${API_BASE_URL}/multi-turn-edit`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error in multi-turn edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}
