/**
 * Service for image generation with text using OpenAI's gpt-image-1 model.
 * Supports initial generation, references, and mask-based editing.
 */

import { ensureFullImageUrl } from "./detect-deployment";

// Timeout configuration (similar to other services)
const REQUEST_TIMEOUT = 120000; // 120 seconds

// Types for image text operations
export interface ImageTextGenerationOptions {
  prompt: string;
}

export interface ImageTextReferenceOptions {
  prompt: string;
  referenceImages: File[];
}

export interface ImageTextMaskOptions {
  prompt: string;
  image: File;
  mask: File;
}

export interface ImageTextResponse {
  success: boolean;
  images?: string[];
  image_url?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

const API_BASE_URL = "/api/v1/image-text";

/**
 * Fetch with timeout support
 */
async function fetchWithTimeout(url: string, options: RequestInit, timeout: number = REQUEST_TIMEOUT): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      ...options,
      signal: controller.signal,
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    if (error instanceof Error && error.name === 'AbortError') {
      throw new Error('Request timeout - image generation took too long');
    }
    throw error;
  }
}

/**
 * Generate an initial image using OpenAI gpt-image-1
 */
export async function generateImageText(options: ImageTextGenerationOptions): Promise<ImageTextResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);

    const response = await fetchWithTimeout(`${API_BASE_URL}/generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error generating image with text:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Generate image using reference images
 */
export async function editImageTextWithReferences(options: ImageTextReferenceOptions): Promise<ImageTextResponse> {
  try {
    if (options.referenceImages.length > 4) {
      return {
        success: false,
        error: "Maximum 4 reference images allowed",
      };
    }

    const formData = new FormData();
    formData.append("prompt", options.prompt);

    options.referenceImages.forEach((image, index) => {
      formData.append("reference_images", image);
    });

    const response = await fetchWithTimeout(`${API_BASE_URL}/edit-with-references`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error generating image with references:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Edit image using mask
 */
export async function editImageTextWithMask(options: ImageTextMaskOptions): Promise<ImageTextResponse> {
  try {
    const formData = new FormData();
    formData.append("prompt", options.prompt);
    formData.append("image", options.image);
    formData.append("mask", options.mask);

    const response = await fetchWithTimeout(`${API_BASE_URL}/edit-with-mask`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error editing image with mask:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/webp"];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: "Tipo de archivo no válido. Use JPEG, PNG o WebP.",
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: "El archivo es demasiado grande. Máximo 10MB.",
    };
  }

  return { valid: true };
}

/**
 * Multi-turn edit functionality
 */
export async function multiTurnEditImageText(previousResponseId: string, editPrompt: string): Promise<ImageTextResponse> {
  try {
    const formData = new FormData();
    formData.append("previous_response_id", previousResponseId);
    formData.append("edit_prompt", editPrompt);

    const response = await fetch(`${API_BASE_URL}/multi-turn-edit`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (data.success && data.image_url) {
      return {
        ...data,
        image_url: ensureFullImageUrl(data.image_url),
      };
    }

    return data;
  } catch (error) {
    console.error("Error in multi-turn edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Validate prompt
 */
export async function validatePrompt(prompt: string): Promise<{ valid: boolean; errors?: string[]; warnings?: string[] }> {
  try {
    const formData = new FormData();
    formData.append("prompt", prompt);

    const response = await fetch(`${API_BASE_URL}/validate-prompt`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error validating prompt:", error);
    return {
      valid: false,
      errors: [error instanceof Error ? error.message : "Unknown error occurred"],
    };
  }
}

/**
 * Get service configuration
 */
export async function getServiceConfig(): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/config`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error getting service config:", error);
    return null;
  }
}

/**
 * Get available models
 */
export async function getAvailableModels(): Promise<any> {
  try {
    const response = await fetch(`${API_BASE_URL}/models`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error getting available models:", error);
    return null;
  }
}

/**
 * Health check
 */
export async function healthCheck(): Promise<{ status: string; message: string }> {
  try {
    const response = await fetch(`${API_BASE_URL}/health`);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error in health check:", error);
    return {
      status: "error",
      message: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Utility function to convert canvas to blob
 */
export function canvasToBlob(canvas: HTMLCanvasElement, type: string = "image/png", quality?: number): Promise<Blob> {
  return new Promise((resolve, reject) => {
    canvas.toBlob((blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new Error("Failed to convert canvas to blob"));
      }
    }, type, quality);
  });
}

/**
 * Utility function to convert blob to file
 */
export function blobToFile(blob: Blob, filename: string): File {
  return new File([blob], filename, { type: blob.type });
}

/**
 * Utility function to resize image
 */
export function resizeImage(file: File, maxWidth: number, maxHeight: number, quality: number = 0.8): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;

      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }

      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      // Set canvas size
      canvas.width = width;
      canvas.height = height;

      // Draw resized image
      ctx?.drawImage(img, 0, 0, width, height);

      // Convert to blob and then to file
      canvas.toBlob((blob) => {
        if (blob) {
          const resizedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now(),
          });
          resolve(resizedFile);
        } else {
          reject(new Error("Failed to resize image"));
        }
      }, file.type, quality);
    };

    img.onerror = () => reject(new Error("Failed to load image"));
    img.src = URL.createObjectURL(file);
  });
}
