/**
 * Service for logo with text generation using OpenAI's gpt-image-1 model.
 * Supports initial generation, references, and mask-based editing.
 */

import { ensureFullImageUrl } from "./detect-deployment";

// Types for logo with text operations
export interface LogoTextGenerationOptions {
  prompt: string;
  size?: "1024x1024" | "1536x1024" | "1024x1536";
}

export interface LogoTextReferenceOptions {
  prompt: string;
  referenceImages: File[];
  size?: "1024x1024" | "1536x1024" | "1024x1536";
}

export interface LogoTextMaskOptions {
  prompt: string;
  image: File;
  mask: File;
}

export interface LogoTextResponse {
  success: boolean;
  images?: string[];
  image_url?: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  error?: string;
}

const API_BASE_URL = "/api/v1/logo-text";

/**
 * Generate an initial logo with text using OpenAI gpt-image-1
 */
export async function generateLogoText(options: LogoTextGenerationOptions): Promise<LogoTextResponse> {
  try {
    // Create the enhanced prompt for logo with text
    const enhancedPrompt = createLogoTextPrompt(options.prompt);

    const formData = new FormData();
    formData.append("prompt", enhancedPrompt);
    formData.append("size", options.size || "1024x1024");

    const response = await fetch(`${API_BASE_URL}/generate`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error generating logo with text:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Generate logo with text using reference images
 */
export async function editWithReferences(options: LogoTextReferenceOptions): Promise<LogoTextResponse> {
  try {
    if (options.referenceImages.length > 4) {
      return {
        success: false,
        error: "Maximum 4 reference images allowed",
      };
    }

    // Create the enhanced prompt for logo with text
    const enhancedPrompt = createLogoTextPrompt(options.prompt);

    const formData = new FormData();
    formData.append("prompt", enhancedPrompt);
    formData.append("size", options.size || "1024x1024");

    options.referenceImages.forEach((image, index) => {
      formData.append("reference_images", image);
    });

    const response = await fetch(`${API_BASE_URL}/edit-with-references`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error in logo text reference edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Edit logo with text using a mask to specify areas to change
 */
export async function editWithMask(options: LogoTextMaskOptions): Promise<LogoTextResponse> {
  try {
    // Create the enhanced prompt for logo with text
    const enhancedPrompt = createLogoTextPrompt(options.prompt);

    const formData = new FormData();
    formData.append("prompt", enhancedPrompt);
    formData.append("image", options.image);
    formData.append("mask", options.mask);

    const response = await fetch(`${API_BASE_URL}/edit-with-mask`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    // Ensure image URLs are properly formatted
    if (result.success && result.image_url) {
      result.image_url = ensureFullImageUrl(result.image_url);
      if (result.images) {
        result.images = result.images.map((url: string) => ensureFullImageUrl(url));
      }
    }

    return result;
  } catch (error) {
    console.error("Error in logo text mask edit:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
    };
  }
}

/**
 * Create enhanced prompt for logo with text generation
 */
function createLogoTextPrompt(userPrompt: string): string {
  const baseTemplate = `Create a modern, elegant, and minimalist logo design for a brand. The logo should reflect the brand's identity. Use clean lines, a simple and timeless design, and avoid clutter or excessive detail. Include both icon and text versions of the logo if possible. The style should be professional, versatile, and suitable for digital and print use.

User request: ${userPrompt}

Generate a professional logo with text that incorporates the user's specifications while maintaining modern design principles.`;

  return baseTemplate;
}

/**
 * Utility function to validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ["image/jpeg", "image/png", "image/webp"];

  if (!allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: "Tipo de archivo no válido. Solo se permiten JPEG, PNG y WebP.",
    };
  }

  if (file.size > maxSize) {
    return {
      valid: false,
      error: "El archivo es demasiado grande. Máximo 10MB.",
    };
  }

  return { valid: true };
}
