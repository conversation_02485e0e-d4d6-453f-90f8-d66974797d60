import {
  collection,
  doc,
  getDoc,
  getDocs,
  setDoc,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
} from "firebase/firestore";
import { db } from "./config";

/**
 * Intenta ejecutar una operación en Firestore y gestiona correctamente los errores
 * @param {Function} operation - Función a ejecutar
 * @param {string} errorPrefix - Prefijo para el mensaje de error
 */
async function safeFirestoreOperation(operation, errorPrefix) {
  try {
    return await operation();
  } catch (error) {
    const isDev = import.meta.env.DEV;
    // En desarrollo, si el error parece relacionado con credenciales, usamos datos simulados
    if (
      isDev &&
      (error.code === "permission-denied" ||
        error.code === "unauthenticated" ||
        error.code === "unavailable" ||
        error.code === "failed-precondition" ||
        error.message.includes("permission") ||
        error.message.includes("credential") ||
        error.message.includes("authentication"))
    ) {
      // En producción no mostramos esta advertencia
      // Devolver un error controlado que no interrumpa la aplicación
      return {
        success: false,
        error:
          "Error de Firestore en desarrollo - continuando con flujo simulado",
      };
    }

    // Error real que debe ser registrado y manejado apropiadamente
    console.error(`${errorPrefix}:`, error);
    return { success: false, error: error.message };
  }
}

/**
 * Crea un documento en una colección.
 * Si se proporciona un ID, crea un documento con ese ID, de lo contrario genera uno automático.
 */
export async function createDocument(collectionName, data, id = null) {
  return safeFirestoreOperation(async () => {
    let docRef;

    if (id) {
      // Crear documento con ID personalizado
      docRef = doc(db, collectionName, id);
      await setDoc(docRef, {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      return { id, error: null, success: true };
    } else {
      // Crear documento con ID generado automáticamente
      docRef = collection(db, collectionName);
      const newDoc = await addDoc(docRef, {
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      return { id: newDoc.id, error: null, success: true };
    }
  }, "Error al crear documento");
}

/**
 * Obtiene un documento específico por su ID
 */
export async function getDocument(collectionName, documentId) {
  return safeFirestoreOperation(async () => {
    const docRef = doc(db, collectionName, documentId);
    const docSnap = await getDoc(docRef);

    if (docSnap.exists()) {
      return {
        data: { id: docSnap.id, ...docSnap.data() },
        error: null,
        success: true,
      };
    } else {
      return {
        data: null,
        error: "No se encontró el documento",
        success: false,
      };
    }
  }, "Error al obtener documento");
}

/**
 * Actualiza un documento existente
 */
export async function updateDocument(collectionName, documentId, data) {
  return safeFirestoreOperation(async () => {
    const docRef = doc(db, collectionName, documentId);
    await updateDoc(docRef, {
      ...data,
      updatedAt: new Date(),
    });

    return { success: true, error: null };
  }, "Error al actualizar documento");
}

/**
 * Elimina un documento
 */
export async function deleteDocument(collectionName, documentId) {
  return safeFirestoreOperation(async () => {
    const docRef = doc(db, collectionName, documentId);
    await deleteDoc(docRef);

    return { success: true, error: null };
  }, "Error al eliminar documento");
}

/**
 * Obtiene todos los documentos de una colección con filtros opcionales
 */
export async function getDocuments(collectionName, options = {}) {
  return safeFirestoreOperation(async () => {
    const collectionRef = collection(db, collectionName);
    let q = collectionRef;

    // Aplicar filtros si existen
    if (options.filters && options.filters.length > 0) {
      const filterConditions = options.filters.map((filter) =>
        where(filter.field, filter.operator, filter.value),
      );
      q = query(collectionRef, ...filterConditions);
    }

    // Aplicar orden si existe
    if (options.sortBy) {
      const sortDirection = options.sortDirection || "asc";
      q = query(q, orderBy(options.sortBy, sortDirection));
    }

    // Aplicar límite si existe
    if (options.limitTo) {
      q = query(q, limit(options.limitTo));
    }

    const querySnapshot = await getDocs(q);
    const documents = [];

    querySnapshot.forEach((doc) => {
      documents.push({ id: doc.id, ...doc.data() });
    });

    return { data: documents, error: null, success: true };
  }, "Error al obtener documentos");
}

/**
 * Obtiene todos los documentos de una colección que pertenecen a un usuario
 */
export async function getUserDocuments(collectionName, userId) {
  return safeFirestoreOperation(async () => {
    const q = query(
      collection(db, collectionName),
      where("userId", "==", userId),
    );

    const querySnapshot = await getDocs(q);
    const documents = [];

    querySnapshot.forEach((doc) => {
      documents.push({ id: doc.id, ...doc.data() });
    });

    return { data: documents, error: null, success: true };
  }, "Error al obtener documentos del usuario");
}
