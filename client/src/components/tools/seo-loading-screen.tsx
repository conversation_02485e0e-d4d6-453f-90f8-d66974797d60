"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Loader2,
  Activity,
  Timer,
  Clock,
  CheckCircle2,
  AlertTriangle,
  Globe,
  Search,
  FileText,
  Zap,
  X
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { SEOProgressData } from "@/hooks/use-seo-progress";

interface SEOLoadingScreenProps {
  analysisMode: "page" | "website";
  url: string;
  progress?: SEOProgressData;
  onProgressUpdate?: (progress: SEOProgressData) => void;
  isPersistent?: boolean;
  onCancel?: () => void;
}

export default function SEOLoadingScreen({
  analysisMode,
  url,
  progress: externalProgress,
  onProgressUpdate,
  isPersistent = false,
  onCancel
}: SEOLoadingScreenProps) {
  const [internalProgress, setInternalProgress] = useState<SEOProgressData>({
    current_page: 0,
    total_pages: 0,
    status: "Iniciando análisis...",
    processed_urls: [],
    failed_urls: [],
    phase: 'discovery'
  });

  const [elapsedTime, setElapsedTime] = useState(0);
  const [startTime] = useState(Date.now());

  // Usar progreso externo si está disponible, sino usar progreso interno
  const progress = externalProgress || internalProgress;

  // Simular progreso solo si no hay progreso externo
  useEffect(() => {
    if (externalProgress) return; // No simular si hay datos reales

    if (analysisMode === "website") {
      const interval = setInterval(() => {
        setInternalProgress(prev => {
          const newProgress = { ...prev };

          // Simular diferentes fases del análisis
          if (prev.current_page === 0 && prev.total_pages === 0) {
            // Fase de descubrimiento
            newProgress.status = "Descubriendo páginas del sitio web...";
            newProgress.total_pages = Math.floor(Math.random() * 100) + 20;
            newProgress.phase = 'discovery';
          } else if (prev.current_page < prev.total_pages) {
            // Fase de análisis
            newProgress.current_page = prev.current_page + 1;
            newProgress.current_url = `${url}/pagina-${newProgress.current_page}`;
            newProgress.status = `Analizando página ${newProgress.current_page}/${newProgress.total_pages}`;
            newProgress.phase = 'analysis';

            // Agregar URL procesada
            if (newProgress.current_url) {
              newProgress.processed_urls = [...prev.processed_urls.slice(-4), newProgress.current_url];
            }

            // Simular errores ocasionales
            if (Math.random() < 0.05) {
              newProgress.failed_urls = [...prev.failed_urls, newProgress.current_url || ""];
            }

            // Calcular tiempo estimado
            const elapsed = (Date.now() - startTime) / 1000;
            const rate = newProgress.current_page / elapsed;
            const remaining = (newProgress.total_pages - newProgress.current_page) / rate;
            newProgress.estimated_time_remaining = Math.round(remaining);
          } else if (prev.current_page >= prev.total_pages && prev.total_pages > 0) {
            // Fase final
            newProgress.status = "Generando recomendaciones finales...";
            newProgress.phase = 'recommendations';
          }

          return newProgress;
        });
      }, 2000);

      return () => clearInterval(interval);
    } else {
      // Para análisis de página única
      const phases = [
        { status: "Conectando con el sitio web...", phase: 'discovery' as const },
        { status: "Analizando estructura HTML...", phase: 'analysis' as const },
        { status: "Evaluando meta tags...", phase: 'analysis' as const },
        { status: "Verificando enlaces...", phase: 'analysis' as const },
        { status: "Analizando contenido...", phase: 'analysis' as const },
        { status: "Generando recomendaciones...", phase: 'recommendations' as const }
      ];

      let phaseIndex = 0;
      const interval = setInterval(() => {
        if (phaseIndex < phases.length) {
          setInternalProgress(prev => ({
            ...prev,
            status: phases[phaseIndex].status,
            phase: phases[phaseIndex].phase,
            current_page: phaseIndex + 1,
            total_pages: phases.length
          }));
          phaseIndex++;
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [analysisMode, url, startTime, externalProgress]);

  // Actualizar tiempo transcurrido
  useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(Math.floor((Date.now() - startTime) / 1000));
    }, 1000);

    return () => clearInterval(interval);
  }, [startTime]);

  // Calcular porcentaje de progreso
  const getProgressPercentage = () => {
    if (progress.total_pages === 0) return 0;
    return Math.round((progress.current_page / progress.total_pages) * 100);
  };

  // Formatear tiempo
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // Obtener icono y color según la fase
  const getPhaseIcon = () => {
    switch (progress.phase) {
      case 'discovery':
        return <Search className="w-12 h-12 text-white" />;
      case 'analysis':
        return <Activity className="w-12 h-12 text-white animate-pulse" />;
      case 'recommendations':
        return <Zap className="w-12 h-12 text-white" />;
      default:
        return <Activity className="w-12 h-12 text-white animate-bounce" />;
    }
  };

  const getPhaseColor = () => {
    switch (progress.phase) {
      case 'discovery':
        return 'from-blue-500 to-indigo-600';
      case 'analysis':
        return 'from-green-500 to-emerald-600';
      case 'recommendations':
        return 'from-purple-500 to-violet-600';
      default:
        return 'from-blue-500 to-indigo-600';
    }
  };

  return (
    <CardContent className="pt-8">
      <div className="flex flex-col items-center justify-center space-y-8 py-8">
        {/* Icono principal animado */}
        <div className="relative">
          <div className={`w-24 h-24 rounded-full bg-gradient-to-r ${getPhaseColor()} flex items-center justify-center animate-pulse`}>
            {getPhaseIcon()}
          </div>
          <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center animate-ping">
            <Loader2 className="w-4 h-4 text-white animate-spin" />
          </div>

          {/* Indicador de fase */}
          <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
            <Badge variant="secondary" className="text-xs font-medium">
              {progress.phase === 'discovery' && 'Descubrimiento'}
              {progress.phase === 'analysis' && 'Análisis'}
              {progress.phase === 'recommendations' && 'Recomendaciones'}
            </Badge>
          </div>
        </div>

        {/* Título y estado actual */}
        <div className="text-center space-y-2">
          <h3 className="text-2xl font-bold text-blue-600">
            {analysisMode === "website" ? "Análisis Exhaustivo en Progreso" : "Analizando Página"}
          </h3>
          <p className="text-lg text-gray-700 font-medium">
            {progress.status}
          </p>
          {analysisMode === "website" && (
            <div className="space-y-2">
              <p className="text-sm text-gray-500">
                ⏱️ Tiempo estimado: 30-60 minutos • Analizando TODAS las páginas del sitio
              </p>
              {isPersistent && (
                <div className="flex items-center justify-center space-x-4">
                  <p className="text-xs text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
                    💾 Análisis persistente - Puedes cerrar esta página
                  </p>
                  {onCancel && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={onCancel}
                      className="text-red-600 border-red-200 hover:bg-red-50"
                    >
                      <X className="h-3 w-3 mr-1" />
                      Cancelar
                    </Button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Barra de progreso principal */}
        <div className="w-full max-w-md space-y-3">
          <Progress 
            value={getProgressPercentage()} 
            className="h-3 bg-gray-200"
          />
          <div className="flex justify-between text-sm text-gray-600">
            <span>
              {progress.current_page}/{progress.total_pages} páginas
            </span>
            <span>{getProgressPercentage()}% completado</span>
          </div>
        </div>

        {/* Estadísticas en tiempo real */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full max-w-2xl">
          <Card className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Timer className="w-5 h-5 text-blue-500 mr-2" />
              <span className="text-sm font-medium">Tiempo</span>
            </div>
            <p className="text-lg font-bold">{formatTime(elapsedTime)}</p>
          </Card>
          
          <Card className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle2 className="w-5 h-5 text-green-500 mr-2" />
              <span className="text-sm font-medium">Procesadas</span>
            </div>
            <p className="text-lg font-bold text-green-600">{progress.processed_urls.length}</p>
          </Card>
          
          <Card className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mr-2" />
              <span className="text-sm font-medium">Errores</span>
            </div>
            <p className="text-lg font-bold text-yellow-600">{progress.failed_urls.length}</p>
          </Card>
          
          <Card className="p-4 text-center">
            <div className="flex items-center justify-center mb-2">
              <Clock className="w-5 h-5 text-purple-500 mr-2" />
              <span className="text-sm font-medium">Restante</span>
            </div>
            <p className="text-lg font-bold text-purple-600">
              {progress.estimated_time_remaining ? formatTime(progress.estimated_time_remaining) : "--:--"}
            </p>
          </Card>
        </div>

        {/* URL actual siendo procesada */}
        {progress.current_url && analysisMode === "website" && (
          <Card className="w-full max-w-2xl p-4 bg-blue-50 border-blue-200">
            <div className="flex items-center space-x-3">
              <div className="flex-shrink-0">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-blue-900">Analizando actualmente:</p>
                <p className="text-sm text-blue-700 truncate">{progress.current_url}</p>
              </div>
            </div>
          </Card>
        )}

        {/* Lista de páginas procesadas recientemente */}
        {progress.processed_urls.length > 0 && analysisMode === "website" && (
          <Card className="w-full max-w-2xl">
            <div className="p-4 border-b">
              <h4 className="font-medium flex items-center">
                <FileText className="w-4 h-4 mr-2 text-green-500" />
                Páginas Procesadas Recientemente
              </h4>
            </div>
            <ScrollArea className="h-32">
              <div className="p-4 space-y-2">
                {progress.processed_urls.slice(-5).map((url, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-500 flex-shrink-0" />
                    <span className="truncate text-gray-600">{url}</span>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </Card>
        )}

        {/* Lista de errores si los hay */}
        {progress.failed_urls.length > 0 && analysisMode === "website" && (
          <Card className="w-full max-w-2xl border-yellow-200 bg-yellow-50">
            <div className="p-4 border-b border-yellow-200">
              <h4 className="font-medium flex items-center text-yellow-800">
                <AlertTriangle className="w-4 h-4 mr-2 text-yellow-600" />
                Páginas con Errores ({progress.failed_urls.length})
              </h4>
            </div>
            <ScrollArea className="h-24">
              <div className="p-4 space-y-2">
                {progress.failed_urls.slice(-3).map((url, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <AlertTriangle className="w-4 h-4 text-yellow-600 flex-shrink-0" />
                    <span className="truncate text-yellow-700">{url}</span>
                  </div>
                ))}
                {progress.failed_urls.length > 3 && (
                  <p className="text-xs text-yellow-600 text-center">
                    +{progress.failed_urls.length - 3} errores más
                  </p>
                )}
              </div>
            </ScrollArea>
          </Card>
        )}

        {/* Información del proceso */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 max-w-2xl">
          <div className="flex items-start space-x-3">
            <Zap className="w-6 h-6 text-blue-600 flex-shrink-0 mt-1" />
            <div>
              <h4 className="font-semibold text-blue-900 mb-2">
                {analysisMode === "website" ? "Análisis Exhaustivo REAL" : "Análisis Rápido"}
              </h4>
              <p className="text-sm text-blue-700 leading-relaxed">
                {analysisMode === "website"
                  ? "Estamos analizando REALMENTE cada página de tu sitio web con más de 100 factores SEO. Este proceso incluye descubrimiento exhaustivo de URLs, análisis técnico profundo, evaluación de contenido y generación de recomendaciones personalizadas con IA. ⚠️ PROCESO REAL - NO SIMULADO."
                  : "Analizando la página específica con más de 50 factores SEO incluyendo meta tags, estructura, contenido, enlaces y optimización técnica."
                }
              </p>
            </div>
          </div>
        </div>

        {/* Mensaje motivacional */}
        <div className="text-center">
          <p className="text-lg font-semibold text-gray-800 mb-2">
            🚀 El Marketing Ya Cambió
          </p>
          <p className="text-sm text-gray-600">
            Emma está optimizando tu presencia digital con inteligencia artificial
          </p>
        </div>
      </div>
    </CardContent>
  );
}
