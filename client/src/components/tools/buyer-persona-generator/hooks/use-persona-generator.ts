/**
 * Custom hook for managing buyer persona generation state
 */

import { useState, useRef, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  FormData, 
  GenerationResult, 
  ViewMode, 
  PremiumFeatureData, 
  PremiumFeatureType,
  LoadingState 
} from '../types';
import { PersonaDataManager } from '../utils/data-manager';
import { PersonaApiService } from '../utils/api-service';
import { PersonaHistoryManager } from '../utils/history-manager';

// Form validation schema
const formSchema = z.object({
  product_description: z.string().min(50, {
    message: "La descripción debe tener al menos 50 caracteres.",
  }),
  industry: z.string().optional(),
  target_market: z.string().optional(),
  business_goals: z.string().optional(),
  competitors: z.string().optional(),
  num_personas: z.number().min(1).max(5).optional(),
  target_countries: z.array(z.string()).optional(),
});

export function usePersonaGenerator() {
  // Main state
  const [viewMode, setViewMode] = useState<ViewMode>("form");
  const [result, setResult] = useState<GenerationResult | null>(null);
  const [selectedPersonaIndex, setSelectedPersonaIndex] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Loading state
  const [loadingState, setLoadingState] = useState<LoadingState>({
    isLoading: false,
    currentStage: 0,
    progressValue: 0,
  });

  // Premium features state
  const [premiumData, setPremiumData] = useState<PremiumFeatureData | null>(null);
  const [loadingPremium, setLoadingPremium] = useState<string | null>(null);

  // History state
  const [showHistory, setShowHistory] = useState(false);

  // Refs
  const resultsRef = useRef<HTMLDivElement>(null);

  // Form setup
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      product_description: "",
      industry: "",
      target_market: "",
      business_goals: "",
      competitors: "",
      num_personas: 3,
      target_countries: [],
    },
  });

  // Progress messages
  const progressMessages = [
    "🔍 Analizando la descripción del producto...",
    "🎯 Identificando segmentos de mercado clave...",
    "👥 Modelando perfiles demográficos...",
    "📝 Creando historias personales relevantes...",
    "🎯 Definiendo motivaciones y objetivos...",
    "⚡ Mapeando puntos de dolor y desafíos...",
    "🛒 Elaborando procesos de compra...",
    "💭 Finalizando perfiles de buyer personas...",
  ];

  // Restore state from localStorage on mount
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const showResults = urlParams.get('showResults');
    const selectedPersona = urlParams.get('selectedPersona');

    if (showResults === 'true') {
      const savedData = PersonaDataManager.loadGenerationResult();
      const savedPremiumData = PersonaDataManager.loadPremiumData();

      if (savedData) {
        setResult(savedData);
        setViewMode("results");

        // Find selected persona index
        if (selectedPersona && savedData.buyer_personas) {
          const personaIndex = savedData.buyer_personas.findIndex(
            (p) => p.name === selectedPersona
          );
          if (personaIndex >= 0) {
            setSelectedPersonaIndex(personaIndex);
          }
        }

        // Load premium data if exists
        if (savedPremiumData) {
          setPremiumData(savedPremiumData);
        }

        // Scroll to results after delay
        setTimeout(() => {
          resultsRef.current?.scrollIntoView({ behavior: "smooth" });
        }, 500);
      }

      // Clean URL parameters
      window.history.replaceState({}, '', window.location.pathname);
    }
  }, []);

  // Generate buyer personas
  const generatePersonas = async (values: FormData) => {
    setLoadingState({
      isLoading: true,
      currentStage: 0,
      progressValue: 0,
    });
    setError(null);
    setViewMode("generating");

    // Simulate progress
    const progressInterval = setInterval(() => {
      setLoadingState(prev => {
        const newStage = Math.min(prev.currentStage + 1, progressMessages.length - 1);
        const newProgressValue = (newStage / (progressMessages.length - 1)) * 100;
        return {
          ...prev,
          currentStage: newStage,
          progressValue: newProgressValue,
        };
      });
    }, 1500);

    try {
      const generationResult = await PersonaApiService.generatePersonas(values);
      
      setResult(generationResult);
      setViewMode("results");

      // Save to localStorage
      PersonaDataManager.saveGenerationResult(generationResult);
      PersonaDataManager.saveProductDescription(values.product_description);

      // Save to history
      PersonaHistoryManager.saveToHistory(
        generationResult,
        null, // Premium data will be added later when loaded
        values.product_description.split('\n')[0] || 'Nueva Generación' // Extract first line as product name
      );

      // Scroll to results after delay
      setTimeout(() => {
        resultsRef.current?.scrollIntoView({ behavior: "smooth" });
      }, 500);

    } catch (err: any) {
      setError(PersonaApiService.handleApiError(err));
      setViewMode("form");
    } finally {
      clearInterval(progressInterval);
      setLoadingState(prev => ({ ...prev, isLoading: false }));
    }
  };

  // Load premium feature
  const loadPremiumFeature = async (featureType: PremiumFeatureType) => {
    console.log(`🚀 Loading premium feature: ${featureType}`);

    if (!result?.buyer_personas[selectedPersonaIndex]) {
      console.error("❌ No persona data available");
      return;
    }

    setLoadingPremium(featureType);

    try {
      const persona = result.buyer_personas[selectedPersonaIndex];
      const productDescription = result.original_product_description || 
                                PersonaDataManager.loadProductDescription() || 
                                form.getValues().product_description;

      const featureData = await PersonaApiService.loadPremiumFeature(
        featureType,
        persona,
        productDescription
      );

      setPremiumData(prev => {
        const newData = {
          ...prev,
          [featureType]: featureData
        };

        // Save to localStorage
        PersonaDataManager.savePremiumData(newData);

        // Update history with premium data if we have a result
        if (result) {
          PersonaHistoryManager.saveToHistory(
            result,
            newData,
            result.original_product_description?.split('\n')[0] || 'Generación con Premium'
          );
        }

        return newData;
      });

    } catch (err: any) {
      console.error(`Error in ${featureType}:`, err);
      setError(`Error en ${featureType}: ${PersonaApiService.handleApiError(err)}`);
    } finally {
      setLoadingPremium(null);
    }
  };

  // Load all premium features
  const loadAllPremiumFeatures = () => {
    const features: PremiumFeatureType[] = ["avatars", "behavior", "conversation", "geographic"];
    features.forEach((feature, index) => {
      setTimeout(() => loadPremiumFeature(feature), index * 1000);
    });
  };

  // Cancel generation
  const cancelGeneration = () => {
    setLoadingState(prev => ({ ...prev, isLoading: false }));
    setViewMode("form");
  };

  // Back to form
  const backToForm = () => {
    setViewMode("form");
    setResult(null);
    setSelectedPersonaIndex(0);
    setPremiumData(null);
    setError(null);

    // Clear localStorage
    PersonaDataManager.clearAllData();
    console.log("🧹 Cleared localStorage cache");

    form.reset();
  };

  // Open conversation simulator
  const openConversationSimulator = (personaIndex?: number) => {
    if (!result?.buyer_personas) return;

    const targetPersonaIndex = personaIndex ?? selectedPersonaIndex;
    const persona = result.buyer_personas[targetPersonaIndex];
    
    if (!persona) return;

    // Prepare complete conversation data
    const conversationData = PersonaDataManager.prepareConversationData(
      persona,
      premiumData || undefined,
      result.original_product_description
    );

    // Generate persona ID
    const personaId = PersonaDataManager.convertNameToId(persona.name);

    // Update localStorage with complete persona data
    PersonaDataManager.updatePersonaInStorage(persona.name, {
      ...persona,
      avatar_description: premiumData?.avatars?.avatar_url || persona.avatar_description,
    });

    // Open simulator in new tab
    const url = `/dashboard/herramientas/buyer-persona-generator/simulador/${personaId}`;
    console.log("🔗 Opening conversation simulator:", url);
    console.log("👤 Conversation data:", conversationData);
    window.open(url, '_blank');
  };

  // History management
  const loadFromHistory = (historyResult: GenerationResult) => {
    setResult(historyResult);
    setViewMode("results");
    setSelectedPersonaIndex(0);
    setPremiumData(null);
    setError(null);

    // Save to current localStorage
    PersonaDataManager.saveGenerationResult(historyResult);
    if (historyResult.original_product_description) {
      PersonaDataManager.saveProductDescription(historyResult.original_product_description);
    }

    // Scroll to results after delay
    setTimeout(() => {
      resultsRef.current?.scrollIntoView({ behavior: "smooth" });
    }, 500);
  };

  const toggleHistory = () => {
    setShowHistory(!showHistory);
  };

  return {
    // State
    viewMode,
    result,
    selectedPersonaIndex,
    error,
    loadingState,
    premiumData,
    loadingPremium,
    progressMessages,
    showHistory,

    // Form
    form,

    // Refs
    resultsRef,

    // Actions
    generatePersonas,
    loadPremiumFeature,
    loadAllPremiumFeatures,
    cancelGeneration,
    backToForm,
    openConversationSimulator,
    setSelectedPersonaIndex,
    setError,
    loadFromHistory,
    toggleHistory,
  };
}
