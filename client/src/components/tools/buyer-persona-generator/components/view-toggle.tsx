/**
 * View toggle component for switching between grid and tabs view
 */

import { motion } from "framer-motion";
import { Grid3X3, List } from "lucide-react";

interface ViewToggleProps {
  viewMode: "grid" | "tabs";
  onToggle: (mode: "grid" | "tabs") => void;
}

export function ViewToggle({ viewMode, onToggle }: ViewToggleProps) {
  return (
    <div className="flex items-center justify-center gap-2 bg-white/80 backdrop-blur-sm rounded-lg border border-[#3018ef]/20 p-1">
      <motion.button
        onClick={() => onToggle("grid")}
        className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-300 ${
          viewMode === "grid"
            ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white shadow-lg"
            : "text-gray-600 hover:text-[#3018ef] hover:bg-[#3018ef]/5"
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <Grid3X3 className="h-4 w-4" />
        <span className="text-sm font-medium">Vista Cuadrícula</span>
      </motion.button>

      <motion.button
        onClick={() => onToggle("tabs")}
        className={`flex items-center gap-2 px-4 py-2 rounded-md transition-all duration-300 ${
          viewMode === "tabs"
            ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white shadow-lg"
            : "text-gray-600 hover:text-[#3018ef] hover:bg-[#3018ef]/5"
        }`}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <List className="h-4 w-4" />
        <span className="text-sm font-medium">Vista Detallada</span>
      </motion.button>
    </div>
  );
}
