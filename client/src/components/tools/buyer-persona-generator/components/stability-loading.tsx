/**
 * Professional loading screen for Stability AI image generation
 */

import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Sparkles, Image, Palette, Zap } from "lucide-react";

interface StabilityLoadingProps {
  isVisible: boolean;
  progress?: number;
  stage?: string;
}

export function StabilityLoading({ 
  isVisible, 
  progress = 0, 
  stage = "Generando avatar con IA..." 
}: StabilityLoadingProps) {
  if (!isVisible) return null;

  const stages = [
    { icon: Sparkles, text: "Analizando características del persona", color: "text-[#3018ef]" },
    { icon: Palette, text: "Creando prompt optimizado para IA", color: "text-[#dd3a5a]" },
    { icon: Image, text: "Generando imagen con Stability AI", color: "text-purple-600" },
    { icon: Zap, text: "Optimizando y finalizando avatar", color: "text-green-600" },
  ];

  const currentStageIndex = Math.min(Math.floor(progress / 25), stages.length - 1);
  const currentStage = stages[currentStageIndex];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
    >
      <Card className="w-full max-w-md bg-white/95 backdrop-blur-xl border-0 shadow-2xl">
        <CardContent className="p-8 text-center">
          {/* Emma Branding */}
          <div className="mb-6">
            <motion.div
              className="mx-auto mb-4 text-6xl"
              animate={{
                scale: [1, 1.1, 1],
                rotate: [0, 5, -5, 0],
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              🎨
            </motion.div>
            
            <h3 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent mb-2">
              Generando Avatar IA
            </h3>
            
            <p className="text-lg font-medium bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
              El Marketing Ya Cambió
            </p>
          </div>

          {/* Current Stage */}
          <div className="mb-6">
            <div className="flex items-center justify-center gap-3 mb-3">
              <currentStage.icon className={`h-6 w-6 ${currentStage.color}`} />
              <span className="text-gray-700 font-medium">{currentStage.text}</span>
            </div>
          </div>

          {/* Progress Bar */}
          <div className="space-y-4 mb-6">
            <div className="flex justify-between items-center">
              <span className="text-sm font-semibold text-gray-700">Progreso</span>
              <span className="text-sm font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                {Math.round(progress)}%
              </span>
            </div>
            <div className="relative">
              <Progress value={progress} className="h-3 bg-gray-200" />
              <div className="absolute inset-0 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-full opacity-20 animate-pulse"></div>
            </div>
          </div>

          {/* Stage Indicators */}
          <div className="grid grid-cols-4 gap-2 mb-6">
            {stages.map((stageItem, index) => {
              const isCompleted = index < currentStageIndex;
              const isCurrent = index === currentStageIndex;
              
              return (
                <div
                  key={index}
                  className={`flex flex-col items-center gap-1 p-2 rounded-lg transition-all duration-300 ${
                    isCompleted 
                      ? "bg-green-50 border border-green-200" 
                      : isCurrent 
                        ? "bg-gradient-to-r from-[#3018ef]/10 to-[#dd3a5a]/10 border border-[#3018ef]/20" 
                        : "bg-gray-50 border border-gray-200"
                  }`}
                >
                  <stageItem.icon 
                    className={`h-4 w-4 ${
                      isCompleted 
                        ? "text-green-600" 
                        : isCurrent 
                          ? stageItem.color 
                          : "text-gray-400"
                    }`} 
                  />
                  <div className={`w-2 h-2 rounded-full ${
                    isCompleted 
                      ? "bg-green-500" 
                      : isCurrent 
                        ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] animate-pulse" 
                        : "bg-gray-300"
                  }`} />
                </div>
              );
            })}
          </div>

          {/* Info */}
          <div className="bg-gradient-to-r from-[#3018ef]/5 to-[#dd3a5a]/5 border border-[#3018ef]/10 p-4 rounded-lg">
            <p className="text-sm text-gray-600">
              🤖 Utilizando Stability AI para crear un avatar único y realista
            </p>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
