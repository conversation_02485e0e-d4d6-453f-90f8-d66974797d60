/**
 * Mode toggle component for switching between text and smart form modes
 */

import { motion } from "framer-motion";
import { FileText, Zap } from "lucide-react";

interface ModeToggleProps {
  isSmartMode: boolean;
  onToggle: (isSmartMode: boolean) => void;
}

export function ModeToggle({ isSmartMode, onToggle }: ModeToggleProps) {
  return (
    <div className="flex items-center justify-center gap-4 p-4 bg-white/80 backdrop-blur-sm rounded-lg border border-[#3018ef]/20 shadow-lg">
      <div className="flex items-center gap-3">
        <FileText className={`h-5 w-5 ${!isSmartMode ? 'text-[#3018ef]' : 'text-gray-400'}`} />
        <span className={`font-medium ${!isSmartMode ? 'text-[#3018ef]' : 'text-gray-500'}`}>
          Modo Texto
        </span>
      </div>

      <button
        onClick={() => onToggle(!isSmartMode)}
        className="relative inline-flex h-8 w-14 items-center rounded-full bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] transition-colors focus:outline-none focus:ring-2 focus:ring-[#3018ef] focus:ring-offset-2"
      >
        <motion.div
          className="inline-block h-6 w-6 transform rounded-full bg-white shadow-lg"
          animate={{ x: isSmartMode ? 28 : 4 }}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
        />
      </button>

      <div className="flex items-center gap-3">
        <Zap className={`h-5 w-5 ${isSmartMode ? 'text-[#3018ef]' : 'text-gray-400'}`} />
        <span className={`font-medium ${isSmartMode ? 'text-[#3018ef]' : 'text-gray-500'}`}>
          Formulario Inteligente
        </span>
      </div>
    </div>
  );
}
