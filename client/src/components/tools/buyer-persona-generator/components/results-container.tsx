/**
 * Results container for displaying buyer personas and premium features
 */

import { useState } from "react";
import { motion } from "framer-motion";
import { PersonaTabsView } from "../../buyer-persona/persona-tabs-view";
import { PersonaGridView } from "./persona-grid-view";
import { ViewToggle } from "./view-toggle";
import { ExportButtons } from "./export-buttons";
import { GenerationResult, PremiumFeatureData, PremiumFeatureType } from "../types";

interface ResultsContainerProps {
  result: GenerationResult;
  selectedPersonaIndex: number;
  premiumData: PremiumFeatureData | null;
  loadingPremium: string | null;
  onPersonaSelect: (index: number) => void;
  onLoadFeature: (feature: PremiumFeatureType) => void;
  onLoadAll: () => void;
  onOpenConversationSimulator: (personaIndex?: number) => void;
}

export function ResultsContainer({
  result,
  selectedPersonaIndex,
  premiumData,
  loadingPremium,
  onPersonaSelect,
  onLoadFeature,
  onLoadAll,
  onOpenConversationSimulator,
}: ResultsContainerProps) {
  const [viewMode, setViewMode] = useState<"grid" | "tabs">("tabs");

  // Focus on tabs view as the primary interface
  const selectedPersona = result.buyer_personas[selectedPersonaIndex];

  if (!selectedPersona) {
    return (
      <div className="text-center py-12">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-red-200/50">
          <div className="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white text-2xl">❌</span>
          </div>
          <p className="text-gray-600 text-lg font-medium">No se encontró la persona seleccionada.</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.3 }}
      className="space-y-6"
    >
      {/* Enhanced Persona Navigation */}
      <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-[#3018ef]/20">
        <div className="text-center mb-6">
          <div className="flex items-center justify-center gap-3 mb-3">
            <div className="w-10 h-10 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] rounded-xl flex items-center justify-center">
              <span className="text-white text-lg">👥</span>
            </div>
            <h2 className="text-2xl font-bold bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
              Buyer Personas Generados
            </h2>
          </div>
          <p className="text-gray-600 font-medium mb-4">
            {viewMode === "tabs"
              ? `Selecciona una persona para ver su análisis completo (${result.buyer_personas.length} disponibles)`
              : "Vista estructurada de tus buyer personas"
            }
          </p>

          {/* View Toggle */}
          <ViewToggle viewMode={viewMode} onToggle={setViewMode} />
        </div>

        {/* Persona Selection (only for tabs view) */}
        {viewMode === "tabs" && (
          <>
            <div className="flex justify-center flex-wrap gap-4">
              {result.buyer_personas.map((persona, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  className="cursor-pointer"
                >
                  <div
                    onClick={() => onPersonaSelect(index)}
                    className={`relative p-6 rounded-2xl border-2 transition-all duration-300 min-w-[200px] ${
                      selectedPersonaIndex === index
                        ? "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white border-transparent shadow-2xl"
                        : "bg-white border-gray-200 text-gray-700 hover:border-[#3018ef] hover:shadow-lg"
                    }`}
                  >
                    <div className="text-center">
                      <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-3 ${
                        selectedPersonaIndex === index
                          ? "bg-white/20"
                          : "bg-gradient-to-r from-[#3018ef] to-[#dd3a5a]"
                      }`}>
                        <span className={`text-lg ${
                          selectedPersonaIndex === index ? "text-white" : "text-white"
                        }`}>
                          👤
                        </span>
                      </div>
                      <h3 className="font-bold text-lg mb-1">{persona.name}</h3>
                      <p className={`text-sm ${
                        selectedPersonaIndex === index ? "text-blue-100" : "text-gray-500"
                      }`}>
                        {persona.job.title}
                      </p>
                      <p className={`text-xs mt-1 ${
                        selectedPersonaIndex === index ? "text-blue-200" : "text-gray-400"
                      }`}>
                        {persona.age} años • {persona.location}
                      </p>
                    </div>

                    {selectedPersonaIndex === index && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-white rounded-full flex items-center justify-center">
                        <span className="text-[#3018ef] text-sm font-bold">✓</span>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>

            {result.buyer_personas.length > 1 && (
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-500 bg-blue-50 px-4 py-2 rounded-lg inline-block">
                  💡 Cada persona representa un segmento único de tu mercado objetivo
                </p>
              </div>
            )}
          </>
        )}
      </div>

      {/* Conditional View Rendering */}
      {viewMode === "grid" ? (
        <PersonaGridView
          personas={result.buyer_personas}
          premiumData={premiumData}
          onOpenConversationSimulator={onOpenConversationSimulator}
        />
      ) : (
        <>
          {/* Export Buttons */}
          <ExportButtons
            result={result}
            premiumData={premiumData}
            selectedPersonaIndex={selectedPersonaIndex}
            personaName={selectedPersona.name}
          />

          {/* Enhanced Tabs View */}
          <PersonaTabsView
            persona={selectedPersona}
            premiumData={premiumData}
            onOpenConversationSimulator={() => onOpenConversationSimulator(selectedPersonaIndex)}
          />
        </>
      )}
    </motion.div>
  );
}
