/**
 * Export Buttons Component for Buyer Persona Generator
 * Provides PDF and CSV export functionality
 */

import { useState } from "react";
import { motion } from "framer-motion";
import { Download, FileText, Table, Loader2, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { PersonaExportService } from "../utils/export-service";
import { GenerationResult, PremiumFeatureData } from "../types";

interface ExportButtonsProps {
  result: GenerationResult;
  premiumData?: PremiumFeatureData | null;
  selectedPersonaIndex?: number;
  personaName?: string;
}

export function ExportButtons({ 
  result, 
  premiumData, 
  selectedPersonaIndex,
  personaName 
}: ExportButtonsProps) {
  const [exportingPDF, setExportingPDF] = useState(false);
  const [exportingCSV, setExportingCSV] = useState(false);
  const [exportSuccess, setExportSuccess] = useState<'pdf' | 'csv' | null>(null);

  const handlePDFExport = async () => {
    try {
      setExportingPDF(true);
      await PersonaExportService.exportToPDF(result, premiumData, selectedPersonaIndex);
      setExportSuccess('pdf');
      setTimeout(() => setExportSuccess(null), 3000);
    } catch (error) {
      console.error('Error exporting PDF:', error);
      // You could add a toast notification here
    } finally {
      setExportingPDF(false);
    }
  };

  const handleCSVExport = () => {
    try {
      setExportingCSV(true);
      PersonaExportService.exportToCSV(result, premiumData, selectedPersonaIndex);
      setExportSuccess('csv');
      setTimeout(() => setExportSuccess(null), 3000);
    } catch (error) {
      console.error('Error exporting CSV:', error);
      // You could add a toast notification here
    } finally {
      setExportingCSV(false);
    }
  };

  const isExportingSingle = selectedPersonaIndex !== undefined;
  const exportCount = isExportingSingle ? 1 : result.buyer_personas.length;

  return (
    <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Download className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">
                Exportar {isExportingSingle ? 'Persona' : 'Reporte'}
              </h3>
              <p className="text-sm text-gray-600">
                {isExportingSingle 
                  ? `Exportar datos de ${personaName || 'la persona seleccionada'}`
                  : `Exportar ${exportCount} buyer personas completas`
                }
              </p>
            </div>
          </div>
          
          {/* Premium Badge */}
          {premiumData && (
            <Badge className="bg-gradient-to-r from-purple-500 to-blue-500 text-white">
              <span className="text-xs">✨ Con Datos Premium</span>
            </Badge>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* PDF Export */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              onClick={handlePDFExport}
              disabled={exportingPDF}
              className="w-full h-auto p-4 bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-700 transition-all duration-200"
              variant="outline"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                  {exportingPDF ? (
                    <Loader2 className="h-5 w-5 text-red-600 animate-spin" />
                  ) : exportSuccess === 'pdf' ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <FileText className="h-5 w-5 text-red-600" />
                  )}
                </div>
                <div className="text-left">
                  <div className="font-semibold">
                    {exportingPDF ? 'Generando PDF...' : 
                     exportSuccess === 'pdf' ? '¡PDF Descargado!' : 'Exportar PDF'}
                  </div>
                  <div className="text-xs text-gray-500">
                    Reporte completo con formato profesional
                  </div>
                </div>
              </div>
            </Button>
          </motion.div>

          {/* CSV Export */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              onClick={handleCSVExport}
              disabled={exportingCSV}
              className="w-full h-auto p-4 bg-white hover:bg-gray-50 border-2 border-gray-200 hover:border-green-300 text-gray-700 hover:text-green-700 transition-all duration-200"
              variant="outline"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  {exportingCSV ? (
                    <Loader2 className="h-5 w-5 text-green-600 animate-spin" />
                  ) : exportSuccess === 'csv' ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <Table className="h-5 w-5 text-green-600" />
                  )}
                </div>
                <div className="text-left">
                  <div className="font-semibold">
                    {exportingCSV ? 'Generando CSV...' : 
                     exportSuccess === 'csv' ? '¡CSV Descargado!' : 'Exportar CSV'}
                  </div>
                  <div className="text-xs text-gray-500">
                    Datos estructurados para análisis
                  </div>
                </div>
              </div>
            </Button>
          </motion.div>
        </div>

        {/* Export Info */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-start gap-2">
            <div className="w-4 h-4 bg-blue-500 rounded-full mt-0.5 flex-shrink-0" />
            <div className="text-xs text-blue-700">
              <div className="font-medium mb-1">Contenido del Export:</div>
              <ul className="space-y-1">
                <li>• Información demográfica y psicográfica completa</li>
                <li>• Objetivos, desafíos y proceso de compra</li>
                <li>• Canales de comunicación y preferencias</li>
                {premiumData && <li>• <strong>Análisis premium con IA</strong> (comportamiento, predicciones)</li>}
                {!isExportingSingle && <li>• Recomendaciones de marketing personalizadas</li>}
              </ul>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 flex items-center justify-between text-xs text-gray-500">
          <span>
            {isExportingSingle ? '1 persona' : `${exportCount} personas`} • 
            {premiumData ? ' Con datos premium' : ' Datos básicos'}
          </span>
          <span>
            Formato: PDF profesional / CSV para análisis
          </span>
        </div>
      </CardContent>
    </Card>
  );
}
