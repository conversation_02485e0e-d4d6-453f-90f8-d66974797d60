/**
 * History Manager for Buyer Persona Generator
 * Manages generation history in localStorage (ready for Supabase integration)
 */

import { GenerationResult, PremiumFeatureData } from '../types';

interface HistoryItem {
  id: string;
  result: GenerationResult;
  timestamp: string;
  personaCount: number;
  productName?: string;
  hasPremiumData: boolean;
  premiumData?: PremiumFeatureData;
}

export class PersonaHistoryManager {
  private static readonly STORAGE_KEY = 'buyer_personas_history';
  private static readonly MAX_HISTORY_ITEMS = 50; // Limit to prevent localStorage bloat

  /**
   * Save a generation result to history
   */
  static saveToHistory(
    result: GenerationResult,
    premiumData?: PremiumFeatureData | null,
    productName?: string
  ): void {
    try {
      const historyItem: HistoryItem = {
        id: result.request_id || this.generateId(),
        result,
        timestamp: new Date().toISOString(),
        personaCount: result.buyer_personas.length,
        productName: productName || this.extractProductName(result),
        hasPremiumData: !!premiumData,
        premiumData: premiumData || undefined
      };

      const existingHistory = this.getAllHistory();
      
      // Remove existing item with same ID if it exists
      const filteredHistory = existingHistory.filter(item => item.id !== historyItem.id);
      
      // Add new item at the beginning
      const updatedHistory = [historyItem, ...filteredHistory];
      
      // Limit history size
      const limitedHistory = updatedHistory.slice(0, this.MAX_HISTORY_ITEMS);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedHistory));
      
      console.log('✅ Generation saved to history:', historyItem.id);
    } catch (error) {
      console.error('❌ Failed to save to history:', error);
    }
  }

  /**
   * Get all history items
   */
  static getAllHistory(): HistoryItem[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      if (!data) return [];
      
      const history = JSON.parse(data) as HistoryItem[];
      
      // Validate and clean up invalid items
      return history.filter(item => 
        item.id && 
        item.result && 
        item.timestamp && 
        Array.isArray(item.result.buyer_personas)
      );
    } catch (error) {
      console.error('❌ Failed to load history:', error);
      return [];
    }
  }

  /**
   * Get a specific history item by ID
   */
  static getHistoryItem(id: string): HistoryItem | null {
    try {
      const history = this.getAllHistory();
      return history.find(item => item.id === id) || null;
    } catch (error) {
      console.error('❌ Failed to get history item:', error);
      return null;
    }
  }

  /**
   * Delete a specific history item
   */
  static deleteHistoryItem(id: string): boolean {
    try {
      const history = this.getAllHistory();
      const filteredHistory = history.filter(item => item.id !== id);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredHistory));
      
      console.log('✅ History item deleted:', id);
      return true;
    } catch (error) {
      console.error('❌ Failed to delete history item:', error);
      return false;
    }
  }

  /**
   * Clear all history
   */
  static clearAllHistory(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('🧹 All history cleared');
    } catch (error) {
      console.error('❌ Failed to clear history:', error);
    }
  }

  /**
   * Get history statistics
   */
  static getHistoryStats(): {
    totalGenerations: number;
    totalPersonas: number;
    premiumGenerations: number;
    oldestGeneration?: string;
    newestGeneration?: string;
  } {
    try {
      const history = this.getAllHistory();
      
      if (history.length === 0) {
        return {
          totalGenerations: 0,
          totalPersonas: 0,
          premiumGenerations: 0
        };
      }

      const totalPersonas = history.reduce((sum, item) => sum + item.personaCount, 0);
      const premiumGenerations = history.filter(item => item.hasPremiumData).length;
      
      // Sort by timestamp to get oldest and newest
      const sortedHistory = [...history].sort((a, b) => 
        new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
      );

      return {
        totalGenerations: history.length,
        totalPersonas,
        premiumGenerations,
        oldestGeneration: sortedHistory[0]?.timestamp,
        newestGeneration: sortedHistory[sortedHistory.length - 1]?.timestamp
      };
    } catch (error) {
      console.error('❌ Failed to get history stats:', error);
      return {
        totalGenerations: 0,
        totalPersonas: 0,
        premiumGenerations: 0
      };
    }
  }

  /**
   * Search history items
   */
  static searchHistory(query: string): HistoryItem[] {
    try {
      const history = this.getAllHistory();
      const searchQuery = query.toLowerCase().trim();
      
      if (!searchQuery) return history;
      
      return history.filter(item => {
        return (
          item.productName?.toLowerCase().includes(searchQuery) ||
          item.result.buyer_personas.some(persona => 
            persona.name.toLowerCase().includes(searchQuery) ||
            persona.job.title.toLowerCase().includes(searchQuery) ||
            persona.job.industry.toLowerCase().includes(searchQuery)
          ) ||
          item.id.toLowerCase().includes(searchQuery)
        );
      });
    } catch (error) {
      console.error('❌ Failed to search history:', error);
      return [];
    }
  }

  /**
   * Export history to JSON
   */
  static exportHistory(): string {
    try {
      const history = this.getAllHistory();
      return JSON.stringify(history, null, 2);
    } catch (error) {
      console.error('❌ Failed to export history:', error);
      return '[]';
    }
  }

  /**
   * Import history from JSON
   */
  static importHistory(jsonData: string): boolean {
    try {
      const importedHistory = JSON.parse(jsonData) as HistoryItem[];
      
      // Validate imported data
      if (!Array.isArray(importedHistory)) {
        throw new Error('Invalid history format');
      }

      const existingHistory = this.getAllHistory();
      
      // Merge histories, avoiding duplicates
      const mergedHistory = [...existingHistory];
      
      importedHistory.forEach(importedItem => {
        if (!mergedHistory.find(existing => existing.id === importedItem.id)) {
          mergedHistory.push(importedItem);
        }
      });

      // Limit and save
      const limitedHistory = mergedHistory
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, this.MAX_HISTORY_ITEMS);
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(limitedHistory));
      
      console.log('✅ History imported successfully');
      return true;
    } catch (error) {
      console.error('❌ Failed to import history:', error);
      return false;
    }
  }

  /**
   * Get recent history (last N items)
   */
  static getRecentHistory(limit: number = 10): HistoryItem[] {
    try {
      const history = this.getAllHistory();
      return history.slice(0, limit);
    } catch (error) {
      console.error('❌ Failed to get recent history:', error);
      return [];
    }
  }

  /**
   * Check if storage is getting full
   */
  static isStorageNearLimit(): boolean {
    try {
      const history = this.getAllHistory();
      return history.length >= this.MAX_HISTORY_ITEMS * 0.9; // 90% of limit
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate a unique ID
   */
  private static generateId(): string {
    return `persona_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Extract product name from generation result
   */
  private static extractProductName(result: GenerationResult): string {
    // Try to extract from original description
    if (result.original_product_description) {
      const lines = result.original_product_description.split('\n');
      const productLine = lines.find(line => 
        line.includes('Producto:') || 
        line.includes('Servicio:') || 
        line.includes('Nombre:')
      );
      
      if (productLine) {
        const match = productLine.match(/(?:Producto|Servicio|Nombre):\s*(.+)/);
        if (match && match[1]) {
          return match[1].trim();
        }
      }
    }

    // Fallback to request ID or timestamp
    return `Generación ${result.request_id?.slice(0, 8) || new Date().toLocaleDateString()}`;
  }

  /**
   * Prepare for Supabase migration
   * This method will help transition from localStorage to Supabase
   */
  static prepareForSupabaseMigration(): {
    historyItems: HistoryItem[];
    totalSize: number;
    needsMigration: boolean;
  } {
    const history = this.getAllHistory();
    const totalSize = JSON.stringify(history).length;
    
    return {
      historyItems: history,
      totalSize,
      needsMigration: history.length > 0
    };
  }
}
