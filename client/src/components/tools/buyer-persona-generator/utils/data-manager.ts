/**
 * Data management utilities for Buyer Persona Generator
 */

import { GenerationResult, BuyerPersona, ConversationData, PremiumFeatureData } from '../types';

export class PersonaDataManager {
  private static readonly STORAGE_KEYS = {
    RESULT: 'buyer_personas_result',
    PRODUCT_DESCRIPTION: 'buyer_personas_product_description',
    PREMIUM_DATA: 'buyer_personas_premium_data',
  } as const;

  /**
   * Save buyer persona generation result to localStorage
   */
  static saveGenerationResult(result: GenerationResult): void {
    try {
      localStorage.setItem(this.STORAGE_KEYS.RESULT, JSON.stringify(result));
      console.log('✅ Generation result saved to localStorage');
    } catch (error) {
      console.error('❌ Failed to save generation result:', error);
    }
  }

  /**
   * Load buyer persona generation result from localStorage
   */
  static loadGenerationResult(): GenerationResult | null {
    try {
      const data = localStorage.getItem(this.STORAGE_KEYS.RESULT);
      if (data) {
        const result = JSON.parse(data) as GenerationResult;
        console.log('✅ Generation result loaded from localStorage');
        return result;
      }
    } catch (error) {
      console.error('❌ Failed to load generation result:', error);
    }
    return null;
  }

  /**
   * Save product description to localStorage
   */
  static saveProductDescription(description: string): void {
    try {
      localStorage.setItem(this.STORAGE_KEYS.PRODUCT_DESCRIPTION, description);
    } catch (error) {
      console.error('❌ Failed to save product description:', error);
    }
  }

  /**
   * Load product description from localStorage
   */
  static loadProductDescription(): string | null {
    try {
      return localStorage.getItem(this.STORAGE_KEYS.PRODUCT_DESCRIPTION);
    } catch (error) {
      console.error('❌ Failed to load product description:', error);
      return null;
    }
  }

  /**
   * Save premium feature data to localStorage
   */
  static savePremiumData(data: PremiumFeatureData): void {
    try {
      localStorage.setItem(this.STORAGE_KEYS.PREMIUM_DATA, JSON.stringify(data));
      console.log('✅ Premium data saved to localStorage');
    } catch (error) {
      console.error('❌ Failed to save premium data:', error);
    }
  }

  /**
   * Load premium feature data from localStorage
   */
  static loadPremiumData(): PremiumFeatureData | null {
    try {
      const data = localStorage.getItem(this.STORAGE_KEYS.PREMIUM_DATA);
      if (data) {
        return JSON.parse(data) as PremiumFeatureData;
      }
    } catch (error) {
      console.error('❌ Failed to load premium data:', error);
    }
    return null;
  }

  /**
   * Convert BuyerPersona to ConversationData format
   */
  static convertPersonaToConversationData(
    persona: BuyerPersona,
    avatarUrl?: string,
    productContext?: string
  ): ConversationData {
    return {
      name: persona.name,
      age: persona.age,
      job: persona.job,
      goals: persona.goals,
      challenges: persona.challenges,
      personal_background: persona.personal_background,
      influences: persona.influences,
      objections: persona.objections,
      communication_channels: persona.communication_channels,
      avatar_url: avatarUrl,
      product_context: productContext,
    };
  }

  /**
   * Find persona by name from generation result
   */
  static findPersonaByName(result: GenerationResult, name: string): BuyerPersona | null {
    if (!result.buyer_personas) return null;
    
    return result.buyer_personas.find(persona => 
      persona.name.toLowerCase() === name.toLowerCase()
    ) || null;
  }

  /**
   * Find persona by ID (name converted to slug)
   */
  static findPersonaById(result: GenerationResult, personaId: string): BuyerPersona | null {
    if (!result.buyer_personas) return null;
    
    return result.buyer_personas.find(persona => 
      this.convertNameToId(persona.name) === personaId
    ) || null;
  }

  /**
   * Convert persona name to URL-safe ID
   */
  static convertNameToId(name: string): string {
    return name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  }

  /**
   * Prepare complete conversation data for simulator
   */
  static prepareConversationData(
    persona: BuyerPersona,
    premiumData?: PremiumFeatureData,
    productDescription?: string
  ): ConversationData {
    const avatarUrl = premiumData?.avatars?.avatar_url || premiumData?.avatars?.avatar_base64 
      ? `data:image/png;base64,${premiumData.avatars.avatar_base64}` 
      : undefined;

    return this.convertPersonaToConversationData(
      persona,
      avatarUrl,
      productDescription || this.loadProductDescription() || undefined
    );
  }

  /**
   * Update persona data in localStorage with additional information
   */
  static updatePersonaInStorage(
    personaName: string,
    updates: Partial<BuyerPersona>
  ): boolean {
    try {
      const result = this.loadGenerationResult();
      if (!result || !result.buyer_personas) return false;

      const personaIndex = result.buyer_personas.findIndex(
        p => p.name === personaName
      );

      if (personaIndex === -1) return false;

      result.buyer_personas[personaIndex] = {
        ...result.buyer_personas[personaIndex],
        ...updates,
      };

      this.saveGenerationResult(result);
      return true;
    } catch (error) {
      console.error('❌ Failed to update persona in storage:', error);
      return false;
    }
  }

  /**
   * Clear all stored data
   */
  static clearAllData(): void {
    try {
      Object.values(this.STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
      console.log('🧹 All buyer persona data cleared from localStorage');
    } catch (error) {
      console.error('❌ Failed to clear data:', error);
    }
  }

  /**
   * Generate realistic fallback persona data
   */
  static generateFallbackPersona(personaId: string): ConversationData {
    const names = [
      "Carlos Mendoza", "María Fernández", "Diego Ramírez", "Ana García",
      "Roberto Silva", "Patricia González", "Javier Morales", "Elena Castro",
      "Alejandro Torres", "Carmen Rodríguez", "Miguel Ruiz", "Isabel Martín",
    ];

    const randomName = names[Math.floor(Math.random() * names.length)];

    return {
      name: randomName,
      age: 35,
      job: {
        title: "Manager",
        industry: "Tecnología",
        company_size: "Mediana",
        responsibilities: ["Gestión de equipos", "Toma de decisiones", "Planificación estratégica"]
      },
      goals: ["Mejorar eficiencia", "Aumentar productividad", "Optimizar procesos"],
      challenges: ["Falta de tiempo", "Presupuesto limitado", "Resistencia al cambio"],
      personal_background: "Profesional experimentado con enfoque en resultados y eficiencia.",
      influences: ["Colegas del sector", "Expertos en la industria", "Estudios de caso"],
      objections: ["Precio elevado", "Complejidad de implementación", "ROI incierto"],
      communication_channels: ["Email", "LinkedIn", "Reuniones presenciales"],
    };
  }
}
