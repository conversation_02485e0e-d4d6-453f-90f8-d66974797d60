/**
 * Export Service for Buyer Persona Generator
 * Handles PDF and CSV export functionality
 */

import jsPDF from 'jspdf';
import { BuyerPersona, GenerationResult, PremiumFeatureData, MarketingRecommendation } from '../types';

export class PersonaExportService {
  
  /**
   * Export personas to PDF
   */
  static async exportToPDF(
    result: GenerationResult,
    premiumData?: PremiumFeatureData | null,
    selectedPersonaIndex?: number
  ): Promise<void> {
    try {
      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      let yPosition = 20;

      // Helper function to add new page if needed
      const checkPageBreak = (requiredSpace: number) => {
        if (yPosition + requiredSpace > pageHeight - 20) {
          pdf.addPage();
          yPosition = 20;
        }
      };

      // Helper function to add text with word wrap
      const addWrappedText = (text: string, x: number, y: number, maxWidth: number, fontSize: number = 10) => {
        pdf.setFontSize(fontSize);
        const lines = pdf.splitTextToSize(text, maxWidth);
        pdf.text(lines, x, y);
        return lines.length * (fontSize * 0.4); // Return height used
      };

      // Title Page
      pdf.setFontSize(24);
      pdf.setTextColor(48, 24, 239); // Emma blue
      pdf.text('Buyer Personas Report', pageWidth / 2, 30, { align: 'center' });
      
      pdf.setFontSize(14);
      pdf.setTextColor(100, 100, 100);
      pdf.text('Generado por Emma Studio', pageWidth / 2, 45, { align: 'center' });
      
      pdf.setFontSize(10);
      pdf.text(`Fecha: ${new Date().toLocaleDateString('es-ES')}`, pageWidth / 2, 55, { align: 'center' });
      pdf.text(`ID de Generación: ${result.request_id}`, pageWidth / 2, 65, { align: 'center' });

      yPosition = 80;

      // Product Description
      if (result.original_product_description) {
        checkPageBreak(30);
        pdf.setFontSize(16);
        pdf.setTextColor(0, 0, 0);
        pdf.text('Descripción del Producto/Servicio', 20, yPosition);
        yPosition += 10;
        
        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        const descHeight = addWrappedText(result.original_product_description, 20, yPosition, pageWidth - 40);
        yPosition += descHeight + 15;
      }

      // Export specific persona or all personas
      const personasToExport = selectedPersonaIndex !== undefined 
        ? [result.buyer_personas[selectedPersonaIndex]]
        : result.buyer_personas;

      // Generate PDF for each persona
      for (let i = 0; i < personasToExport.length; i++) {
        const persona = personasToExport[i];
        
        // New page for each persona (except first if on title page)
        if (i > 0 || yPosition > 100) {
          pdf.addPage();
          yPosition = 20;
        }

        // Persona Header
        pdf.setFontSize(20);
        pdf.setTextColor(48, 24, 239);
        pdf.text(`${persona.name}`, 20, yPosition);
        yPosition += 15;

        // Basic Info
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text('Información Básica', 20, yPosition);
        yPosition += 8;

        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        const basicInfo = [
          `Edad: ${persona.age} años`,
          `Género: ${persona.gender}`,
          `Ubicación: ${persona.location}`,
          `Educación: ${persona.education}`,
          `Nivel de Ingresos: ${persona.income_level}`,
          `Estado Civil: ${persona.marital_status}`
        ];

        basicInfo.forEach(info => {
          pdf.text(info, 25, yPosition);
          yPosition += 5;
        });
        yPosition += 5;

        // Job Information
        checkPageBreak(25);
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text('Información Profesional', 20, yPosition);
        yPosition += 8;

        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        pdf.text(`Cargo: ${persona.job.title}`, 25, yPosition);
        yPosition += 5;
        pdf.text(`Industria: ${persona.job.industry}`, 25, yPosition);
        yPosition += 5;
        pdf.text(`Tamaño de Empresa: ${persona.job.company_size}`, 25, yPosition);
        yPosition += 10;

        // Personal Background
        checkPageBreak(20);
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text('Trasfondo Personal', 20, yPosition);
        yPosition += 8;

        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        const bgHeight = addWrappedText(persona.personal_background, 25, yPosition, pageWidth - 50);
        yPosition += bgHeight + 10;

        // Goals
        checkPageBreak(30);
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text('Objetivos', 20, yPosition);
        yPosition += 8;

        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        persona.goals.forEach(goal => {
          checkPageBreak(8);
          pdf.text(`• ${goal}`, 25, yPosition);
          yPosition += 6;
        });
        yPosition += 5;

        // Challenges
        checkPageBreak(30);
        pdf.setFontSize(12);
        pdf.setTextColor(0, 0, 0);
        pdf.text('Desafíos', 20, yPosition);
        yPosition += 8;

        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        persona.challenges.forEach(challenge => {
          checkPageBreak(8);
          pdf.text(`• ${challenge}`, 25, yPosition);
          yPosition += 6;
        });
        yPosition += 10;

        // Premium Data Section
        if (premiumData) {
          this.addPremiumDataToPDF(pdf, premiumData, yPosition, pageWidth, checkPageBreak);
        }
      }

      // Marketing Recommendations
      if (result.marketing_recommendations && result.marketing_recommendations.length > 0) {
        pdf.addPage();
        yPosition = 20;

        pdf.setFontSize(18);
        pdf.setTextColor(48, 24, 239);
        pdf.text('Recomendaciones de Marketing', 20, yPosition);
        yPosition += 15;

        result.marketing_recommendations.forEach(rec => {
          checkPageBreak(40);
          
          pdf.setFontSize(14);
          pdf.setTextColor(0, 0, 0);
          pdf.text(`Para: ${rec.persona_name}`, 20, yPosition);
          yPosition += 10;

          // Content Types
          pdf.setFontSize(12);
          pdf.text('Tipos de Contenido:', 25, yPosition);
          yPosition += 6;
          
          pdf.setFontSize(10);
          pdf.setTextColor(60, 60, 60);
          rec.content_types.forEach(type => {
            pdf.text(`• ${type}`, 30, yPosition);
            yPosition += 5;
          });
          yPosition += 5;

          // Messaging Tips
          pdf.setFontSize(12);
          pdf.setTextColor(0, 0, 0);
          pdf.text('Consejos de Mensajería:', 25, yPosition);
          yPosition += 6;
          
          pdf.setFontSize(10);
          pdf.setTextColor(60, 60, 60);
          rec.messaging_tips.forEach(tip => {
            checkPageBreak(8);
            const tipHeight = addWrappedText(`• ${tip}`, 30, yPosition, pageWidth - 60);
            yPosition += tipHeight + 2;
          });
          yPosition += 10;
        });
      }

      // Save PDF
      const fileName = selectedPersonaIndex !== undefined 
        ? `buyer-persona-${personasToExport[0].name.replace(/\s+/g, '-').toLowerCase()}.pdf`
        : `buyer-personas-report-${new Date().toISOString().split('T')[0]}.pdf`;
      
      pdf.save(fileName);
      
    } catch (error) {
      console.error('Error exporting to PDF:', error);
      throw new Error('Error al exportar a PDF');
    }
  }

  /**
   * Add premium data to PDF
   */
  private static addPremiumDataToPDF(
    pdf: jsPDF,
    premiumData: PremiumFeatureData,
    startY: number,
    pageWidth: number,
    checkPageBreak: (space: number) => void
  ): number {
    let yPosition = startY;

    // Behavior Analysis
    if (premiumData.behavior?.predictions) {
      checkPageBreak(30);
      
      pdf.setFontSize(14);
      pdf.setTextColor(48, 24, 239);
      pdf.text('Análisis de Comportamiento Premium', 20, yPosition);
      yPosition += 10;

      const predictions = premiumData.behavior.predictions;
      
      if (predictions.purchase_probability) {
        pdf.setFontSize(10);
        pdf.setTextColor(60, 60, 60);
        pdf.text(`Probabilidad de Compra: ${Math.round(predictions.purchase_probability)}%`, 25, yPosition);
        yPosition += 6;
      }

      if (predictions.lead_quality_score) {
        pdf.text(`Puntuación de Lead: ${Math.round(predictions.lead_quality_score)}/100`, 25, yPosition);
        yPosition += 6;
      }

      if (predictions.price_sensitivity?.willing_to_pay) {
        pdf.text(`Disposición a Pagar: ${predictions.price_sensitivity.willing_to_pay}`, 25, yPosition);
        yPosition += 10;
      }
    }

    return yPosition;
  }

  /**
   * Export personas to CSV
   */
  static exportToCSV(
    result: GenerationResult,
    premiumData?: PremiumFeatureData | null,
    selectedPersonaIndex?: number
  ): void {
    try {
      const personasToExport = selectedPersonaIndex !== undefined 
        ? [result.buyer_personas[selectedPersonaIndex]]
        : result.buyer_personas;

      // CSV Headers
      const headers = [
        'Nombre',
        'Edad',
        'Género',
        'Ubicación',
        'Educación',
        'Nivel de Ingresos',
        'Estado Civil',
        'Cargo',
        'Industria',
        'Tamaño de Empresa',
        'Trasfondo Personal',
        'Objetivos',
        'Desafíos',
        'Canales de Comunicación',
        'Influencias',
        'Objeciones',
        'Marcas Preferidas',
        'Día Típico'
      ];

      // Add premium data headers if available
      if (premiumData?.behavior?.predictions) {
        headers.push('Probabilidad de Compra (%)', 'Puntuación de Lead', 'Disposición a Pagar');
      }

      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...personasToExport.map(persona => {
          const row = [
            `"${persona.name}"`,
            persona.age,
            `"${persona.gender}"`,
            `"${persona.location}"`,
            `"${persona.education}"`,
            `"${persona.income_level}"`,
            `"${persona.marital_status}"`,
            `"${persona.job.title}"`,
            `"${persona.job.industry}"`,
            `"${persona.job.company_size}"`,
            `"${persona.personal_background.replace(/"/g, '""')}"`,
            `"${persona.goals.join('; ')}"`,
            `"${persona.challenges.join('; ')}"`,
            `"${persona.communication_channels.join('; ')}"`,
            `"${persona.influences.join('; ')}"`,
            `"${persona.objections.join('; ')}"`,
            `"${persona.brand_affinities.join('; ')}"`,
            `"${persona.typical_day.replace(/"/g, '""')}"`
          ];

          // Add premium data if available
          if (premiumData?.behavior?.predictions) {
            row.push(
              premiumData.behavior.predictions.purchase_probability?.toString() || '',
              premiumData.behavior.predictions.lead_quality_score?.toString() || '',
              `"${premiumData.behavior.predictions.price_sensitivity?.willing_to_pay || ''}"`
            );
          }

          return row.join(',');
        })
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      
      link.setAttribute('href', url);
      link.setAttribute('download', 
        selectedPersonaIndex !== undefined 
          ? `buyer-persona-${personasToExport[0].name.replace(/\s+/g, '-').toLowerCase()}.csv`
          : `buyer-personas-${new Date().toISOString().split('T')[0]}.csv`
      );
      
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
    } catch (error) {
      console.error('Error exporting to CSV:', error);
      throw new Error('Error al exportar a CSV');
    }
  }
}
