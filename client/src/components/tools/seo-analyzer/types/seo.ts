// SEO Analyzer Types
export interface SEOBasicInfo {
  title: string;
  title_length: number;
  meta_description: string | null;
  meta_description_length: number;
  h1_tags: string[];
  h1_count: number;
  meta_robots: string | null;
  canonical_url: string | null;
  language: string | null;
}

export interface RankingKeyword {
  keyword: string;
  position: number;
  url: string;
  title: string;
  snippet: string;
  exact_match: boolean;
  domain_match: boolean;
}

export interface KeywordAnalysis {
  top_positions: RankingKeyword[];
  good_positions: RankingKeyword[];
  opportunities: RankingKeyword[];
  average_position: number;
  total_rankings: number;
  best_position?: number;
  worst_position?: number;
}

export interface RankingKeywordsData {
  total_keywords_found: number;
  ranking_keywords: RankingKeyword[];
  keyword_analysis: KeywordAnalysis;
  search_queries_tested: number;
  domain: string;
  error?: string;
}

export interface SEOContentAnalysis {
  word_count: number;
  images: {
    total: number;
    without_alt: number;
  };
  links: {
    total: number;
    internal_count: number;
    external_count: number;
  };
  headings_structure: Record<string, string[]>;
  top_keywords: Array<{
    word: string;
    count: number;
  }>;
  ranking_keywords?: RankingKeywordsData;
}

export interface SEOChecks {
  [key: string]: boolean;
}

export interface SEORecommendation {
  type: string;
  priority: string;
  title: string;
  description: string;
  impact: string;
}

export interface Achievement {
  category: string;
  achievement: string;
  description: string;
  icon: string;
  impact: string;
}

export interface SEOOpenGraph {
  title?: string;
  description?: string;
  image?: string;
  url?: string;
  type?: string;
  site_name?: string;
}

export interface SEOTwitterCard {
  card?: string;
  title?: string;
  description?: string;
  image?: string;
  site?: string;
  creator?: string;
}

export interface SEOPreviewData {
  title: string;
  url: string;
  description: string;
  og_title: string;
  og_description: string;
  og_image?: string;
  twitter_title: string;
  twitter_description: string;
  twitter_image?: string;
}

export interface CoreWebVital {
  value: number | null;
  display_value: string;
  score: number;
  rating: 'good' | 'needs_improvement' | 'poor' | 'unknown';
}

export interface PerformanceMetrics {
  core_web_vitals: {
    largest_contentful_paint: CoreWebVital;
    first_input_delay: CoreWebVital;
    cumulative_layout_shift: CoreWebVital;
    first_contentful_paint: CoreWebVital;
    speed_index: CoreWebVital;
    time_to_interactive: CoreWebVital;
  };
  lighthouse_scores: {
    performance: number;
    accessibility: number;
    best_practices: number;
    seo: number;
  };
  overall_performance_score: number;
}

export interface SEOAnalysisResult {
  status: "success" | "error";
  url: string;
  basic_info: SEOBasicInfo;
  content_analysis: SEOContentAnalysis;
  seo_checks: SEOChecks;
  recommendations: SEORecommendation[];
  achievements: Achievement[];
  open_graph: SEOOpenGraph;
  twitter_card: SEOTwitterCard;
  preview_data: SEOPreviewData;
  performance_metrics?: PerformanceMetrics;
  ai_enhanced: boolean;
  error_message?: string;
}

export interface SEOProgressData {
  status: "running" | "complete" | "error" | "cancelled";
  progress: number;
  current_step: string;
  estimated_completion: string;
  pages_discovered?: number;
  pages_analyzed?: number;
  result?: SEOAnalysisResult;
  error?: {
    error: string;
    details?: string;
  };
}

export interface SEOAnalysisRequest {
  url: string;
  mode: "page" | "website";
  enable_progress?: boolean;
}

export type AnalysisMode = "page" | "website";

export interface SEOAnalyzerProps {
  // Main component props if needed
}

// Component Props Types
export interface SEOAnalysisFormProps {
  url: string;
  setUrl: (url: string) => void;
  analysisMode: AnalysisMode;
  setAnalysisMode: (mode: AnalysisMode) => void;
  onAnalyze: () => void;
  isLoading: boolean;
  progressLoading: boolean;
  persistentLoading: boolean;
  showPulsatingEffect: boolean;
}

export interface SEOProgressDisplayProps {
  isLoading: boolean;
  progressLoading: boolean;
  persistentLoading: boolean;
  persistentProgress: SEOProgressData | null;
  onCancel: () => void;
}

export interface SEOResultsTabsProps {
  data: SEOAnalysisResult;
  url: string;
  getSEOScore: () => number;
  copyToClipboard: (text: string) => void;
}

export interface SEOErrorDisplayProps {
  error: Error | null;
  progressError: string | null;
  persistentError: string | null;
  isError: boolean;
}

export interface SEODebugPanelProps {
  // Debug panel specific props
}
