import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ExternalLink } from "lucide-react";
import { SEOResultsTabsProps } from "../types/seo";
import { SEORecommendationsTab } from "./tabs/SEORecommendationsTab";
import { SEOGeneralTab } from "./tabs/SEOGeneralTab";
import { SEOContentTab } from "./tabs/SEOContentTab";
import { SEOMetaTagsTab } from "./tabs/SEOMetaTagsTab";
import { SEOPreviewTab } from "./tabs/SEOPreviewTab";
import AchievementsTab from "../components/AchievementsTab";
import CoreWebVitalsTab from "./CoreWebVitalsTab";
import RankingKeywordsTab from "./RankingKeywordsTab";

export const SEOResultsTabs: React.FC<SEOResultsTabsProps> = ({
  data,
  url,
  getSEOScore,
  copyToClipboard,
}) => {
  const seoScore = getSEOScore();

  // Debug logging
  console.log('📊 SEOResultsTabs - Full data object:', data);
  console.log('🏆 SEOResultsTabs - Achievements in data:', data.achievements);
  console.log('🏆 SEOResultsTabs - Achievements type:', typeof data.achievements);
  console.log('🏆 SEOResultsTabs - Achievements length:', data.achievements?.length);

  return (
    <Card className="w-full">
      <CardHeader className="pb-2 border-b">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl">
              Resultados del análisis
            </CardTitle>
            <CardDescription>
              <a
                href={data.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center text-blue-600 hover:underline font-medium"
              >
                {data.url} <ExternalLink className="ml-1 h-3 w-3" />
              </a>
            </CardDescription>
          </div>
          <div className="flex flex-col items-center">
            <div className="relative">
              <svg viewBox="0 0 36 36" className="w-16 h-16">
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke="#eeeeee"
                  strokeWidth="3"
                />
                <path
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                  fill="none"
                  stroke={
                    seoScore > 80
                      ? "#4ade80"
                      : seoScore > 50
                        ? "#facc15"
                        : "#f87171"
                  }
                  strokeWidth="3"
                  strokeDasharray={`${seoScore}, 100`}
                  strokeLinecap="round"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-2xl font-bold">
                  {seoScore}
                </div>
              </div>
            </div>
            <div className="text-sm font-medium mt-1">
              Puntuación SEO
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="achievements">
          <TabsList className="grid grid-cols-8 mb-4">
            <TabsTrigger value="achievements">
              🎉 Logros
            </TabsTrigger>
            <TabsTrigger value="keywords">
              🎯 Keywords
            </TabsTrigger>
            <TabsTrigger value="recommendations">
              Recomendaciones
            </TabsTrigger>
            <TabsTrigger value="performance">
              Rendimiento
            </TabsTrigger>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="content">Contenido</TabsTrigger>
            <TabsTrigger value="meta">Meta Tags</TabsTrigger>
            <TabsTrigger value="preview">
              Previsualizaciones
            </TabsTrigger>
          </TabsList>

          <TabsContent value="achievements">
            <AchievementsTab achievements={data.achievements || []} />
          </TabsContent>

          <TabsContent value="keywords">
            {data.content_analysis?.ranking_keywords ? (
              <RankingKeywordsTab data={data.content_analysis.ranking_keywords} />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Datos de ranking de keywords no disponibles</p>
                <p className="text-sm mt-2">
                  Se requiere configuración de Serper API para análisis de keywords reales
                </p>
              </div>
            )}
          </TabsContent>

          <SEORecommendationsTab
            recommendations={data.recommendations}
            url={data.url || url}
          />

          <TabsContent value="performance">
            {data.performance_metrics ? (
              <CoreWebVitalsTab data={data.performance_metrics} />
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>Datos de rendimiento no disponibles</p>
                <p className="text-sm mt-2">
                  Los datos de Core Web Vitals requieren una conexión a Google PageSpeed Insights API
                </p>
              </div>
            )}
          </TabsContent>

          <SEOGeneralTab
            seoChecks={data.seo_checks}
          />

          <SEOContentTab
            contentAnalysis={data.content_analysis}
            basicInfo={data.basic_info}
          />

          <SEOMetaTagsTab
            basicInfo={data.basic_info}
            openGraph={data.open_graph}
            twitterCard={data.twitter_card}
            copyToClipboard={copyToClipboard}
          />

          <SEOPreviewTab
            previewData={data.preview_data}
          />
        </Tabs>
      </CardContent>
    </Card>
  );
};
