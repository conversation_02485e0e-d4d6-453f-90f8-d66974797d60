import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, Globe, FileText } from "lucide-react";
import { SEOAnalysisFormProps } from "../types/seo";

export const SEOAnalysisForm: React.FC<SEOAnalysisFormProps> = ({
  url,
  setUrl,
  analysisMode,
  setAnalysisMode,
  onAnalyze,
  isLoading,
  progressLoading,
  persistentLoading,
  showPulsatingEffect,
}) => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onAnalyze();
  };

  const isAnyLoading = isLoading || progressLoading || persistentLoading;

  return (
    <Card className="w-full">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          Analizador SEO Avanzado
        </CardTitle>
        <p className="text-muted-foreground">
          Analiza el SEO de tu sitio web con inteligencia artificial
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* URL Input */}
          <div className="space-y-2">
            <label htmlFor="url" className="text-sm font-medium">
              URL del sitio web
            </label>
            <div className="relative">
              <Input
                id="url"
                type="url"
                placeholder="https://ejemplo.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="pl-10"
                disabled={isAnyLoading}
              />
              <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            </div>
          </div>

          {/* Analysis Mode Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Tipo de análisis</label>
            <Tabs
              value={analysisMode}
              onValueChange={(value) => setAnalysisMode(value as "page" | "website")}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="page" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Página específica
                </TabsTrigger>
                <TabsTrigger value="website" className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Sitio completo
                </TabsTrigger>
              </TabsList>

              <TabsContent value="page" className="mt-4">
                <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="font-medium text-blue-900 mb-2">
                    Análisis de página específica
                  </h4>
                  <p className="text-sm text-blue-700">
                    Análisis rápido (5-10 segundos) de una página específica.
                    Incluye SEO técnico, contenido, meta tags y recomendaciones
                    con IA.
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="website" className="mt-4">
                <div className="p-4 bg-purple-50 rounded-lg border border-purple-200">
                  <h4 className="font-medium text-purple-900 mb-2">
                    Análisis exhaustivo del sitio
                  </h4>
                  <p className="text-sm text-purple-700">
                    Análisis completo (30-60 minutos) de todo el sitio web.
                    Incluye crawling, análisis de múltiples páginas y reporte
                    detallado. Puedes cerrar la página y volver más tarde.
                  </p>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Analyze Button */}
          <Button
            type="submit"
            className={`w-full h-12 text-lg font-semibold transition-all duration-300 ${
              showPulsatingEffect
                ? "animate-pulse bg-gradient-to-r from-blue-500 to-purple-600"
                : "bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            }`}
            disabled={isAnyLoading || !url.trim()}
          >
            {isAnyLoading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                {analysisMode === "website" ? "Iniciando análisis..." : "Analizando..."}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                {analysisMode === "website" ? "Iniciar análisis completo" : "Analizar página"}
              </div>
            )}
          </Button>

          {/* Analysis Mode Info */}
          {analysisMode === "website" && (
            <div className="text-center text-sm text-muted-foreground">
              <p>
                💡 El análisis completo puede tomar hasta 60 minutos.
                <br />
                Recibirás actualizaciones en tiempo real del progreso.
              </p>
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
};
