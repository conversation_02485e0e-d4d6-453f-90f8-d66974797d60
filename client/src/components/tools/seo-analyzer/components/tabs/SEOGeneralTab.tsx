import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Check, X } from "lucide-react";
import { SEOChecks } from "../../types/seo";
import { formatSEOCheckKey, getSEOCheckDescription } from "../../utils/seo-helpers";

interface SEOGeneralTabProps {
  seoChecks: SEOChecks;
}

export const SEOGeneralTab: React.FC<SEOGeneralTabProps> = ({
  seoChecks,
}) => {
  const renderCheckStatus = (passed: boolean) => {
    return passed ? (
      <Check className="h-4 w-4 text-green-600" />
    ) : (
      <X className="h-4 w-4 text-red-600" />
    );
  };

  return (
    <TabsContent value="general" className="space-y-4">
      <div className="space-y-4">
        {seoChecks &&
          Object.entries(seoChecks).map(([key, value]) => (
            <div
              key={key}
              className="flex items-start space-x-2 p-2 rounded-md border"
            >
              <div className="mt-0.5">
                {renderCheckStatus(Boolean(value))}
              </div>
              <div>
                <h4 className="text-sm font-medium">
                  {formatSEOCheckKey(key)}
                </h4>
                <p className="text-sm text-muted-foreground">
                  {getSEOCheckDescription(key)}
                </p>
              </div>
            </div>
          ))}
      </div>
    </TabsContent>
  );
};
