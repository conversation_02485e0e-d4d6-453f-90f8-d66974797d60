import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { SEOContentAnalysis, SEOBasicInfo } from "../../types/seo";

interface SEOContentTabProps {
  contentAnalysis: SEOContentAnalysis;
  basicInfo: SEOBasicInfo;
}

export const SEOContentTab: React.FC<SEOContentTabProps> = ({
  contentAnalysis,
  basicInfo,
}) => {
  return (
    <TabsContent value="content" className="space-y-4">
      {contentAnalysis && (
        <>
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-base">
                  Estadísticas de contenido
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <ul className="space-y-2">
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">
                      Recuento de palabras:
                    </span>
                    <span className="font-medium">
                      {contentAnalysis.word_count}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">
                      Imágenes totales:
                    </span>
                    <span className="font-medium">
                      {contentAnalysis.images.total}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">
                      Imágenes sin alt:
                    </span>
                    <span className="font-medium">
                      {contentAnalysis.images.without_alt}
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-base">
                  Enlaces
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <ul className="space-y-2">
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">
                      Enlaces totales:
                    </span>
                    <span className="font-medium">
                      {contentAnalysis.links.total}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">
                      Enlaces internos:
                    </span>
                    <span className="font-medium">
                      {contentAnalysis.links.internal_count}
                    </span>
                  </li>
                  <li className="flex justify-between">
                    <span className="text-muted-foreground">
                      Enlaces externos:
                    </span>
                    <span className="font-medium">
                      {contentAnalysis.links.external_count}
                    </span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Headings Structure */}
          <Card>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-base">
                Estructura de encabezados
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              {basicInfo?.h1_tags &&
              basicInfo.h1_tags.length > 0 ? (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">
                    Etiquetas H1 ({basicInfo.h1_tags.length})
                  </h4>
                  <ScrollArea className="h-24 w-full rounded-md border p-2">
                    <ul className="space-y-1">
                      {basicInfo.h1_tags.map((tag, i) => (
                        <li key={i} className="text-sm">
                          {tag}
                        </li>
                      ))}
                    </ul>
                  </ScrollArea>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  No se encontraron etiquetas H1
                </p>
              )}

              {contentAnalysis.headings_structure &&
                Object.entries(contentAnalysis.headings_structure)
                  .filter(([_, headings]) => headings.length > 0)
                  .map(([level, headings]) => (
                    <div key={level} className="mt-4 space-y-2">
                      <h4 className="text-sm font-medium">
                        Etiquetas {level.toUpperCase()} ({headings.length})
                      </h4>
                      <ScrollArea className="h-24 w-full rounded-md border p-2">
                        <ul className="space-y-1">
                          {headings.map((heading, i) => (
                            <li key={i} className="text-sm">
                              {heading}
                            </li>
                          ))}
                        </ul>
                      </ScrollArea>
                    </div>
                  ))}
            </CardContent>
          </Card>

          {/* Keywords */}
          <Card>
            <CardHeader className="p-4 pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-base">
                  Palabras clave principales
                </CardTitle>
              </div>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              {contentAnalysis.top_keywords &&
              contentAnalysis.top_keywords.length > 0 ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {contentAnalysis.top_keywords
                      .slice(0, 6)
                      .map((keyword, i) => (
                        <div
                          key={i}
                          className="flex justify-between items-center p-2 rounded-md border"
                        >
                          <span>{keyword.word}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-xs font-medium">
                              {keyword.count}x
                            </span>
                          </div>
                        </div>
                      ))}
                  </div>
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">
                  No se encontraron palabras clave destacadas
                </p>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </TabsContent>
  );
};
