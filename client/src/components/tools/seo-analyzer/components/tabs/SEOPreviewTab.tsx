import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { SEOPreviewData } from "../../types/seo";

interface SEOPreviewTabProps {
  previewData: SEOPreviewData;
}

export const SEOPreviewTab: React.FC<SEOPreviewTabProps> = ({
  previewData,
}) => {
  return (
    <TabsContent value="preview" className="space-y-4">
      {previewData ? (
        <>
          <Card>
            <CardHeader className="p-4 pb-2">
              <CardTitle className="text-base">
                Previsualización en Google
              </CardTitle>
            </CardHeader>
            <CardContent className="p-4 pt-0">
              <div className="border p-4 rounded-md space-y-1 max-w-2xl">
                <h3 className="text-xl text-blue-600 hover:underline cursor-pointer">
                  {previewData.title}
                </h3>
                <p className="text-green-700 text-sm">
                  {previewData.url}
                </p>
                <p className="text-sm text-gray-600">
                  {previewData.description}
                </p>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-base">
                  Open Graph (Facebook)
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="border p-4 rounded-md space-y-1">
                  <h3 className="font-medium">
                    {previewData.og_title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {previewData.og_description}
                  </p>
                  {previewData.og_image && (
                    <div className="mt-2 p-2 bg-gray-100 text-center rounded">
                      <p className="text-xs text-gray-500">
                        Imagen disponible
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="p-4 pb-2">
                <CardTitle className="text-base">
                  Twitter Card
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 pt-0">
                <div className="border p-4 rounded-md space-y-1">
                  <h3 className="font-medium">
                    {previewData.twitter_title}
                  </h3>
                  <p className="text-sm text-gray-600">
                    {previewData.twitter_description}
                  </p>
                  {previewData.twitter_image && (
                    <div className="mt-2 p-2 bg-gray-100 text-center rounded">
                      <p className="text-xs text-gray-500">
                        Imagen disponible
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      ) : (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>
            No hay previsualizaciones disponibles
          </AlertTitle>
          <AlertDescription>
            No se pudieron generar previsualizaciones para esta
            URL. Asegúrate de que la página tenga meta tags
            correctamente implementadas.
          </AlertDescription>
        </Alert>
      )}
    </TabsContent>
  );
};
