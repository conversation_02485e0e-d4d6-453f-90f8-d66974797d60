import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Copy } from "lucide-react";
import { SEOBasicInfo, SEOOpenGraph, SEOTwitterCard } from "../../types/seo";

interface SEOMetaTagsTabProps {
  basicInfo: SEOBasicInfo;
  openGraph: SEOOpenGraph;
  twitterCard: SEOTwitterCard;
  copyToClipboard: (text: string) => void;
}

export const SEOMetaTagsTab: React.FC<SEOMetaTagsTabProps> = ({
  basicInfo,
  openGraph,
  twitterCard,
  copyToClipboard,
}) => {
  return (
    <TabsContent value="meta" className="space-y-4">
      <Card>
        <CardHeader className="p-4 pb-2">
          <div className="flex justify-between">
            <CardTitle className="text-base">
              Meta Tags Básicos
            </CardTitle>
            <Button
              size="sm"
              variant="outline"
              onClick={() =>
                copyToClipboard(
                  JSON.stringify(basicInfo, null, 2),
                )
              }
            >
              <Copy className="h-4 w-4 mr-1" /> Copiar
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-medium">Título</h4>
              <p className="text-sm border p-2 rounded-md">
                {basicInfo?.title || "No definido"}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Longitud: {basicInfo?.title_length || 0}{" "}
                caracteres (Recomendado: 50-60)
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium">
                Meta Descripción
              </h4>
              <p className="text-sm border p-2 rounded-md">
                {basicInfo?.meta_description ||
                  "No definida"}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Longitud:{" "}
                {basicInfo?.meta_description_length || 0}{" "}
                caracteres (Recomendado: 140-160)
              </p>
            </div>

            <div>
              <h4 className="text-sm font-medium">Robots</h4>
              <p className="text-sm border p-2 rounded-md">
                {basicInfo?.meta_robots || "No definido"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Canonical</h4>
              <p className="text-sm border p-2 rounded-md">
                {basicInfo?.canonical_url || "No definido"}
              </p>
            </div>
            <div>
              <h4 className="text-sm font-medium">Idioma</h4>
              <p className="text-sm border p-2 rounded-md">
                {basicInfo?.language || "No definido"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">
              Open Graph
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            {openGraph &&
            Object.entries(openGraph).filter(
              ([_, v]) => v !== null,
            ).length > 0 ? (
              <ScrollArea className="h-56 w-full">
                <div className="space-y-2">
                  {Object.entries(openGraph)
                    .filter(([_, v]) => v !== null)
                    .map(([key, value]) => (
                      <div key={key} className="space-y-1">
                        <h4 className="text-xs font-medium text-muted-foreground">
                          og:{key}
                        </h4>
                        <p className="text-sm border p-1 rounded-md break-all">
                          {value}
                        </p>
                      </div>
                    ))}
                </div>
              </ScrollArea>
            ) : (
              <p className="text-sm text-muted-foreground">
                No se encontraron etiquetas Open Graph
              </p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">
              Twitter Card
            </CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            {twitterCard &&
            Object.entries(twitterCard).filter(
              ([_, v]) => v !== null,
            ).length > 0 ? (
              <ScrollArea className="h-56 w-full">
                <div className="space-y-2">
                  {Object.entries(twitterCard)
                    .filter(([_, v]) => v !== null)
                    .map(([key, value]) => (
                      <div key={key} className="space-y-1">
                        <h4 className="text-xs font-medium text-muted-foreground">
                          twitter:{key}
                        </h4>
                        <p className="text-sm border p-1 rounded-md break-all">
                          {value}
                        </p>
                      </div>
                    ))}
                </div>
              </ScrollArea>
            ) : (
              <p className="text-sm text-muted-foreground">
                No se encontraron etiquetas Twitter Card
              </p>
            )}
          </CardContent>
        </Card>
      </div>
    </TabsContent>
  );
};
