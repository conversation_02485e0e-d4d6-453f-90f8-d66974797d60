import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  TrendingUp, 
  Target, 
  Search, 
  Trophy,
  AlertCircle,
  ExternalLink,
  BarChart3,
  Zap
} from 'lucide-react';
import { RankingKeywordsData, RankingKeyword } from '../types/seo';

interface RankingKeywordsTabProps {
  data: RankingKeywordsData;
}

const RankingKeywordsTab: React.FC<RankingKeywordsTabProps> = ({ data }) => {
  const getPositionColor = (position: number) => {
    if (position <= 3) return 'text-green-600 bg-green-50 border-green-200';
    if (position <= 10) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getPositionIcon = (position: number) => {
    if (position <= 3) return <Trophy className="w-4 h-4" />;
    if (position <= 10) return <Target className="w-4 h-4" />;
    return <AlertCircle className="w-4 h-4" />;
  };

  const getPositionLabel = (position: number) => {
    if (position <= 3) return 'Top 3';
    if (position <= 10) return 'Primera página';
    return 'Oportunidad';
  };

  if (data.error) {
    return (
      <div className="text-center py-8 text-gray-500">
        <AlertCircle className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className="text-lg font-medium">Análisis de keywords no disponible</p>
        <p className="text-sm mt-2">{data.error}</p>
      </div>
    );
  }

  if (data.total_keywords_found === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Search className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className="text-lg font-medium">No se encontraron rankings</p>
        <p className="text-sm mt-2">
          Esta página no aparece en los primeros 20 resultados para las keywords analizadas
        </p>
        <p className="text-xs mt-1 text-gray-400">
          Se analizaron {data.search_queries_tested} consultas de búsqueda
        </p>
      </div>
    );
  }

  const { keyword_analysis } = data;

  return (
    <div className="space-y-6">
      {/* Explanation Header */}
      <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">
              ¿Qué significa esto?
            </h3>
            <div className="mt-2 text-sm text-blue-700">
              <p>
                Emma busca en Google las palabras clave de tu página y te muestra <strong>en qué posición apareces REALMENTE</strong>.
                No son solo las palabras más frecuentes de tu contenido, sino las keywords por las que Google te está mostrando a usuarios reales cuando buscan.
              </p>
              <p className="mt-2">
                <strong>Top 3:</strong> Excelente posicionamiento • <strong>Primera página:</strong> Buen posicionamiento • <strong>Oportunidades:</strong> Potencial de mejora
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Overview Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{data.total_keywords_found}</div>
            <p className="text-sm text-gray-600">Keywords encontradas</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{keyword_analysis.top_positions.length}</div>
            <p className="text-sm text-gray-600">Top 3 posiciones</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-yellow-600">{keyword_analysis.good_positions.length}</div>
            <p className="text-sm text-gray-600">Primera página</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{keyword_analysis.average_position}</div>
            <p className="text-sm text-gray-600">Posición promedio</p>
          </CardContent>
        </Card>
      </div>

      {/* Top Performing Keywords */}
      {keyword_analysis.top_positions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trophy className="w-5 h-5 text-green-600" />
              Keywords en Top 3
              <Badge variant="outline" className="ml-2 text-green-600">Excelente</Badge>
            </CardTitle>
            <p className="text-sm text-gray-600">
              Estas keywords están posicionadas en los primeros 3 resultados
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {keyword_analysis.top_positions.map((keyword, index) => (
                <KeywordCard key={index} keyword={keyword} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Good Positions */}
      {keyword_analysis.good_positions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="w-5 h-5 text-yellow-600" />
              Keywords en Primera Página
              <Badge variant="outline" className="ml-2 text-yellow-600">Bueno</Badge>
            </CardTitle>
            <p className="text-sm text-gray-600">
              Estas keywords están en la primera página (posiciones 4-10)
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {keyword_analysis.good_positions.map((keyword, index) => (
                <KeywordCard key={index} keyword={keyword} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Opportunities */}
      {keyword_analysis.opportunities.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              Oportunidades de Mejora
              <Badge variant="outline" className="ml-2 text-blue-600">Potencial</Badge>
            </CardTitle>
            <p className="text-sm text-gray-600">
              Keywords con potencial de mejora (posición 11+)
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {keyword_analysis.opportunities.map((keyword, index) => (
                <KeywordCard key={index} keyword={keyword} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

const KeywordCard: React.FC<{ keyword: RankingKeyword }> = ({ keyword }) => {
  const getPositionColor = (position: number) => {
    if (position <= 3) return 'text-green-600 bg-green-50 border-green-200';
    if (position <= 10) return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  const getPositionIcon = (position: number) => {
    if (position <= 3) return <Trophy className="w-4 h-4" />;
    if (position <= 10) return <Target className="w-4 h-4" />;
    return <AlertCircle className="w-4 h-4" />;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`p-4 rounded-lg border ${getPositionColor(keyword.position)}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Search className="w-4 h-4" />
            <span className="font-semibold">{keyword.keyword}</span>
            {keyword.exact_match && (
              <Badge variant="outline" className="text-xs">URL exacta</Badge>
            )}
          </div>
          
          <h4 className="font-medium text-sm mb-1">{keyword.title}</h4>
          <p className="text-xs text-gray-600 mb-2">{keyword.snippet}</p>
          
          <a 
            href={keyword.url} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-xs text-blue-600 hover:underline flex items-center gap-1"
          >
            {keyword.url} <ExternalLink className="w-3 h-3" />
          </a>
        </div>
        
        <div className="text-right ml-4">
          <div className="flex items-center gap-2 mb-1">
            {getPositionIcon(keyword.position)}
            <span className="text-2xl font-bold">#{keyword.position}</span>
          </div>
          <Badge variant="outline" className="text-xs">
            {keyword.position <= 3 ? 'Top 3' : 
             keyword.position <= 10 ? 'Página 1' : 'Página 2+'}
          </Badge>
        </div>
      </div>
    </motion.div>
  );
};

export default RankingKeywordsTab;
