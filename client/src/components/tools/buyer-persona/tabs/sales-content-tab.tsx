/**
 * Sales & Content Strategy tab - Sales strategies and content preferences
 */

import { BuyerPersona, PremiumFeatureData } from "../../buyer-persona-generator/types";

interface SalesContentTabProps {
  persona: BuyerPersona;
  premiumData?: PremiumFeatureData | null;
}

export function SalesContentTab({ persona, premiumData }: SalesContentTabProps) {
  // Generate content recommendations based on persona
  const getContentRecommendations = () => {
    const isExecutive = persona.job.title.toLowerCase().includes('director') || 
                       persona.job.title.toLowerCase().includes('ceo') ||
                       persona.job.title.toLowerCase().includes('manager');
    
    if (isExecutive) {
      return {
        contentTypes: ["Estudios de caso", "Informes de industria", "Webinars ejecutivos", "ROI calculators"],
        topics: ["Estrategia empresarial", "Transformación digital", "Optimización de costos", "Liderazgo"],
        formats: ["Presentaciones ejecutivas", "One-pagers", "Video testimonials", "Demos personalizadas"]
      };
    } else {
      return {
        contentTypes: ["Tutoriales", "Guías técnicas", "Comparativas", "Casos de uso"],
        topics: ["Mejores prácticas", "Implementación", "Productividad", "Herramientas"],
        formats: ["Blog posts", "Videos explicativos", "Infografías", "Documentación técnica"]
      };
    }
  };

  const contentRec = getContentRecommendations();

  const getSalesApproach = () => {
    const decisionFactors = persona.buying_process.decision_factors;
    
    if (decisionFactors.includes('ROI') || decisionFactors.includes('Precio')) {
      return {
        approach: "Consultivo con enfoque en valor",
        keyMessages: [
          "Retorno de inversión demostrable",
          "Reducción de costos operativos",
          "Incremento en eficiencia"
        ],
        salesTactics: [
          "Presentar calculadora de ROI",
          "Mostrar casos de éxito similares",
          "Ofrecer prueba piloto"
        ]
      };
    } else {
      return {
        approach: "Educativo y de construcción de confianza",
        keyMessages: [
          "Solución innovadora y confiable",
          "Soporte técnico especializado",
          "Facilidad de implementación"
        ],
        salesTactics: [
          "Demostración técnica detallada",
          "Referencias de clientes",
          "Plan de implementación gradual"
        ]
      };
    }
  };

  const salesApproach = getSalesApproach();

  return (
    <div className="space-y-6">
      {/* Section Title */}
      <h2 className="text-[#101419] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Estrategia de Ventas
      </h2>

      {/* Strategy Overview */}
      <p className="text-[#101419] text-base font-normal leading-normal pb-3 pt-1 px-4">
        Nuestra estrategia de ventas debe enfatizar un enfoque {salesApproach.approach.toLowerCase()}, 
        enfocándose en cómo nuestra plataforma se alinea con los objetivos y prioridades de {persona.name}. 
        Las preferencias de contenido incluyen {contentRec.contentTypes.slice(0, 2).join(' y ').toLowerCase()} 
        que ofrecen insights accionables. Geográficamente, {persona.name} está ubicado en {persona.location}, 
        y el análisis de mercado indica una creciente demanda de soluciones tecnológicas sostenibles en su región.
      </p>

      {/* Sales Approach */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Enfoque de Ventas</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Estrategia Principal</h4>
              <div className="bg-blue-50 p-4 rounded-lg mb-4">
                <p className="text-blue-800 font-medium">{salesApproach.approach}</p>
              </div>
              <h4 className="text-[#101419] font-semibold mb-3">Mensajes Clave</h4>
              <ul className="space-y-2">
                {salesApproach.keyMessages.map((message, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
                    <span className="text-[#101419] text-sm">{message}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Tácticas de Venta</h4>
              <div className="space-y-3">
                {salesApproach.salesTactics.map((tactic, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 font-semibold text-xs">{index + 1}</span>
                    </div>
                    <span className="text-[#101419] text-sm">{tactic}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Strategy */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Estrategia de Contenido</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Tipos de Contenido</h4>
              <div className="space-y-2">
                {contentRec.contentTypes.map((type, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <span className="text-[#101419] text-sm">{type}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Temas Relevantes</h4>
              <div className="space-y-2">
                {contentRec.topics.map((topic, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                    <div className="w-2 h-2 bg-orange-500 rounded-full" />
                    <span className="text-[#101419] text-sm">{topic}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Formatos Preferidos</h4>
              <div className="space-y-2">
                {contentRec.formats.map((format, index) => (
                  <div key={index} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-[#101419] text-sm">{format}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Geographic and Market Analysis */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Análisis Geográfico y de Mercado</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Información Geográfica</h4>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-[#57738e]">Ubicación:</span>
                  <span className="text-[#101419] font-medium">{persona.location}</span>
                </div>
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-blue-800 font-medium">Consideraciones Locales</p>
                  <p className="text-blue-600 text-sm">
                    Adaptar mensajes a la cultura empresarial local y regulaciones específicas de la región
                  </p>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Tendencias de Mercado</h4>
              <div className="space-y-3">
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-green-800 font-medium">✓ Crecimiento en demanda</p>
                  <p className="text-green-600 text-sm">Soluciones tecnológicas sostenibles</p>
                </div>
                <div className="p-3 bg-yellow-50 rounded-lg">
                  <p className="text-yellow-800 font-medium">⚡ Oportunidad</p>
                  <p className="text-yellow-600 text-sm">Transformación digital acelerada</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Competitive Positioning */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Posicionamiento Competitivo</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Ventajas Competitivas</h4>
              <div className="space-y-3">
                <div className="border-l-4 border-green-400 pl-4">
                  <p className="text-[#101419] font-medium">Innovación Tecnológica</p>
                  <p className="text-[#57738e] text-sm">Soluciones de vanguardia con IA integrada</p>
                </div>
                <div className="border-l-4 border-blue-400 pl-4">
                  <p className="text-[#101419] font-medium">Soporte Especializado</p>
                  <p className="text-[#57738e] text-sm">Equipo técnico dedicado y onboarding personalizado</p>
                </div>
                <div className="border-l-4 border-purple-400 pl-4">
                  <p className="text-[#101419] font-medium">ROI Comprobado</p>
                  <p className="text-[#57738e] text-sm">Casos de éxito documentados en industrias similares</p>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Diferenciadores Clave</h4>
              <div className="space-y-3">
                {persona.brand_affinities.slice(0, 3).map((brand, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <p className="text-[#101419] font-medium">vs. {brand}</p>
                    <p className="text-[#57738e] text-sm">
                      {index === 0 && "Mayor flexibilidad y personalización"}
                      {index === 1 && "Mejor relación precio-valor"}
                      {index === 2 && "Implementación más rápida"}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Premium Content Intelligence */}
      {premiumData?.behavior?.predictions?.content_preferences && (
        <div className="px-4">
          <h3 className="text-[#101419] text-lg font-semibold mb-4">Inteligencia de Contenido Premium</h3>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-purple-500 rounded-full" />
              <span className="text-purple-800 font-semibold">Análisis Avanzado de Preferencias</span>
            </div>
            <p className="text-[#101419]">
              Datos adicionales sobre preferencias de contenido y patrones de consumo están disponibles con el análisis premium.
            </p>
          </div>
        </div>
      )}

      {/* Action Items */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Próximos Pasos</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Contenido a Crear</h4>
              <ul className="space-y-2">
                <li className="flex items-center gap-3">
                  <input type="checkbox" className="rounded" />
                  <span className="text-[#101419] text-sm">Caso de estudio específico para {persona.job.industry}</span>
                </li>
                <li className="flex items-center gap-3">
                  <input type="checkbox" className="rounded" />
                  <span className="text-[#101419] text-sm">Calculadora de ROI personalizada</span>
                </li>
                <li className="flex items-center gap-3">
                  <input type="checkbox" className="rounded" />
                  <span className="text-[#101419] text-sm">Demo adaptada a sus necesidades</span>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Acciones de Ventas</h4>
              <ul className="space-y-2">
                <li className="flex items-center gap-3">
                  <input type="checkbox" className="rounded" />
                  <span className="text-[#101419] text-sm">Preparar presentación ejecutiva</span>
                </li>
                <li className="flex items-center gap-3">
                  <input type="checkbox" className="rounded" />
                  <span className="text-[#101419] text-sm">Identificar referencias relevantes</span>
                </li>
                <li className="flex items-center gap-3">
                  <input type="checkbox" className="rounded" />
                  <span className="text-[#101419] text-sm">Programar seguimiento estratégico</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
