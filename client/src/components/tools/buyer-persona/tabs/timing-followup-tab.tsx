/**
 * Timing & Follow-Up tab - When and how to contact
 */

import { BuyerPersona, PremiumFeatureData } from "../../buyer-persona-generator/types";

interface TimingFollowupTabProps {
  persona: BuyerPersona;
  premiumData?: PremiumFeatureData | null;
}

export function TimingFollowupTab({ persona, premiumData }: TimingFollowupTabProps) {
  // Generate optimal timing based on persona characteristics
  const getOptimalTiming = () => {
    const isExecutive = persona.job.title.toLowerCase().includes('director') || 
                       persona.job.title.toLowerCase().includes('ceo') ||
                       persona.job.title.toLowerCase().includes('manager');
    
    if (isExecutive) {
      return {
        bestDays: "Martes a Jueves",
        bestTimes: "9:00 AM - 11:00 AM",
        worstTimes: "Lunes temprano, Viernes tarde",
        frequency: "Cada 2-3 semanas"
      };
    } else {
      return {
        bestDays: "Miércoles a Viernes",
        bestTimes: "10:00 AM - 12:00 PM, 2:00 PM - 4:00 PM",
        worstTimes: "Lunes, después de las 5:00 PM",
        frequency: "Semanal"
      };
    }
  };

  const timing = premiumData?.behavior?.predictions?.intelligent_timing || getOptimalTiming();

  const getFollowUpStrategy = () => {
    const timeline = persona.buying_process.timeline;
    
    if (timeline.includes('semana')) {
      return {
        phase1: "Contacto inicial con contenido relevante",
        phase2: "Demo personalizada y casos de uso",
        phase3: "Propuesta formal y negociación",
        totalDuration: "2-4 semanas"
      };
    } else {
      return {
        phase1: "Educación y construcción de confianza",
        phase2: "Evaluación de necesidades específicas",
        phase3: "Presentación de solución y cierre",
        totalDuration: "1-3 meses"
      };
    }
  };

  const followUpStrategy = getFollowUpStrategy();

  return (
    <div className="space-y-6">
      {/* Section Title */}
      <h2 className="text-[#101419] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Timing y Seguimiento
      </h2>

      {/* Timing Overview */}
      <p className="text-[#101419] text-base font-normal leading-normal pb-3 pt-1 px-4">
        Basado en los patrones de actividad de {persona.name}, el momento óptimo para contactar es {timing.bestDays?.toLowerCase()}, 
        entre las {timing.bestTimes?.toLowerCase()}. Nuestra estrategia de seguimiento debe enfocarse en proporcionar insights valiosos 
        y abordar objeciones potenciales relacionadas con {persona.objections.slice(0, 2).join(' y ').toLowerCase()}. 
        Anticipamos que las objeciones pueden girar en torno a la asignación de recursos y la curva de aprendizaje asociada con nuevas herramientas.
      </p>

      {/* Optimal Contact Times */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Horarios Óptimos de Contacto</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h4 className="text-[#101419] font-semibold mb-4 flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full" />
              Mejores Momentos
            </h4>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-[#57738e]">Días:</span>
                <span className="text-[#101419] font-medium">{timing.bestDays}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-[#57738e]">Horarios:</span>
                <span className="text-[#101419] font-medium">{timing.bestTimes}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-[#57738e]">Frecuencia:</span>
                <span className="text-[#101419] font-medium">{timing.frequency}</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <h4 className="text-[#101419] font-semibold mb-4 flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full" />
              Evitar
            </h4>
            <div className="space-y-3">
              <div className="bg-red-50 p-3 rounded-lg">
                <p className="text-red-800 font-medium">Horarios a Evitar</p>
                <p className="text-red-600 text-sm">{timing.worstTimes}</p>
              </div>
              <div className="bg-yellow-50 p-3 rounded-lg">
                <p className="text-yellow-800 font-medium">Consideraciones</p>
                <p className="text-yellow-600 text-sm">
                  Evitar contacto durante períodos de alta carga de trabajo o reuniones frecuentes
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Follow-Up Strategy */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Estrategia de Seguimiento</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="space-y-6">
            {/* Timeline Overview */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-blue-800 font-semibold mb-2">Duración Total del Proceso</h4>
              <p className="text-blue-700">{followUpStrategy.totalDuration}</p>
            </div>

            {/* Phases */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="border-l-4 border-green-400 pl-4">
                <h4 className="text-[#101419] font-semibold mb-2">Fase 1</h4>
                <p className="text-[#57738e] text-sm">{followUpStrategy.phase1}</p>
                <div className="mt-2 text-xs text-green-600 font-medium">Semana 1-2</div>
              </div>
              <div className="border-l-4 border-yellow-400 pl-4">
                <h4 className="text-[#101419] font-semibold mb-2">Fase 2</h4>
                <p className="text-[#57738e] text-sm">{followUpStrategy.phase2}</p>
                <div className="mt-2 text-xs text-yellow-600 font-medium">Semana 3-4</div>
              </div>
              <div className="border-l-4 border-blue-400 pl-4">
                <h4 className="text-[#101419] font-semibold mb-2">Fase 3</h4>
                <p className="text-[#57738e] text-sm">{followUpStrategy.phase3}</p>
                <div className="mt-2 text-xs text-blue-600 font-medium">Semana 5+</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Communication Preferences */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Preferencias de Comunicación</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Canales Preferidos</h4>
              <div className="space-y-3">
                {persona.communication_channels.map((channel, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <span className="text-[#101419]">{channel}</span>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }, (_, i) => (
                        <div 
                          key={i} 
                          className={`w-2 h-2 rounded-full ${
                            i < (5 - index) ? 'bg-blue-500' : 'bg-gray-300'
                          }`} 
                        />
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Estilo de Mensaje</h4>
              <div className="space-y-3">
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-green-800 font-medium">✓ Directo y al punto</p>
                  <p className="text-green-600 text-sm">Mensajes concisos con valor claro</p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-green-800 font-medium">✓ Basado en datos</p>
                  <p className="text-green-600 text-sm">Incluir métricas y casos de éxito</p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <p className="text-green-800 font-medium">✓ Profesional</p>
                  <p className="text-green-600 text-sm">Tono formal pero accesible</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Objection Anticipation */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Anticipación de Objeciones</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="space-y-4">
            {persona.objections.map((objection, index) => (
              <div key={index} className="border border-orange-200 rounded-lg p-4">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-orange-600 text-xs font-bold">{index + 1}</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="text-[#101419] font-semibold mb-2">"{objection}"</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <p className="text-[#57738e] text-sm font-medium mb-1">Cuándo abordar:</p>
                        <p className="text-[#101419] text-sm">
                          {index === 0 ? "En la primera conversación" : 
                           index === 1 ? "Durante la demo" : "En la propuesta final"}
                        </p>
                      </div>
                      <div>
                        <p className="text-[#57738e] text-sm font-medium mb-1">Estrategia:</p>
                        <p className="text-[#101419] text-sm">
                          Preparar respuesta con datos específicos y casos de éxito relevantes
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Premium Timing Intelligence */}
      {premiumData?.behavior?.predictions?.intelligent_timing && (
        <div className="px-4">
          <h3 className="text-[#101419] text-lg font-semibold mb-4">Inteligencia de Timing Premium</h3>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-purple-500 rounded-full" />
              <span className="text-purple-800 font-semibold">Análisis Avanzado de Timing</span>
            </div>
            <p className="text-[#101419]">
              Datos adicionales de timing inteligente y patrones de comportamiento están disponibles con el análisis premium.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
