/**
 * Overview tab - Executive summary with photo and key data
 */

import { BuyerPersona, PremiumFeatureData } from "../../buyer-persona-generator/types";

interface OverviewTabProps {
  persona: BuyerPersona;
  premiumData?: PremiumFeatureData | null;
}

export function OverviewTab({ persona, premiumData }: OverviewTabProps) {
  // Get avatar image URL - prioritize auto-generated avatar, then premium, then fallback
  const getAvatarImageUrl = () => {
    // First priority: Auto-generated avatar from persona generation
    if (persona.avatar_url) {
      return persona.avatar_url;
    }

    // Second priority: Premium avatar if available
    if (premiumData?.avatars?.avatar_url) {
      return premiumData.avatars.avatar_url;
    }
    if (premiumData?.avatars?.avatar_base64) {
      return `data:image/png;base64,${premiumData.avatars.avatar_base64}`;
    }

    // Fallback to stock image
    return "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face";
  };

  // Check if we have a generated avatar
  const hasGeneratedAvatar = () => {
    return !!(persona.avatar_url || premiumData?.avatars?.avatar_url || premiumData?.avatars?.avatar_base64);
  };

  return (
    <div className="space-y-8 bg-gradient-to-br from-blue-50/30 to-purple-50/30 min-h-screen">
      {/* Enhanced Section Title */}
      <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] p-6 rounded-2xl mx-4 mt-4">
        <h2 className="text-white text-[24px] font-bold leading-tight tracking-[-0.015em]">
          📊 Resumen General
        </h2>
        <p className="text-blue-100 text-sm mt-1">Vista completa del perfil de buyer persona</p>
      </div>

      {/* Enhanced Persona Header */}
      <div className="mx-4">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-purple-200/50">
          <div className="flex w-full flex-col gap-6 md:flex-row md:justify-between md:items-center">
            <div className="flex gap-6">
              <div className="relative">
                <div
                  className="bg-center bg-no-repeat aspect-square bg-cover rounded-full min-h-36 w-36 border-4 border-gradient-to-r from-[#3018ef] to-[#dd3a5a] shadow-2xl"
                  style={{
                    backgroundImage: `url("${getAvatarImageUrl()}")`,
                    borderImage: 'linear-gradient(45deg, #3018ef, #dd3a5a) 1'
                  }}
                />
                {hasGeneratedAvatar() && (
                  <div className="absolute -bottom-2 -right-2 bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
                    ✨ IA
                  </div>
                )}
              </div>
              <div className="flex flex-col justify-center space-y-2">
                <h1 className="text-[#101419] text-[28px] font-bold leading-tight tracking-[-0.015em] bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] bg-clip-text text-transparent">
                  {persona.name}
                </h1>
                <p className="text-[#57738e] text-lg font-medium leading-normal">
                  {persona.job.title} en {persona.job.industry}
                </p>
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full font-medium">
                    {persona.age} años
                  </span>
                  <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded-full font-medium">
                    {persona.location}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Executive Summary */}
      <div className="mx-4">
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border-l-4 border-gradient-to-b from-[#3018ef] to-[#dd3a5a]">
          <h3 className="text-[#3018ef] text-lg font-bold mb-3 flex items-center gap-2">
            📋 Resumen Ejecutivo
          </h3>
          <p className="text-[#101419] text-base font-normal leading-relaxed">
            <span className="font-semibold text-[#3018ef]">{persona.name}</span> es {persona.personal_background}.
            Sus principales motivadores incluyen <span className="font-medium text-[#dd3a5a]">{persona.goals.slice(0, 2).join(' y ')}</span>,
            y busca soluciones que le permitan superar desafíos como <span className="font-medium text-orange-600">{persona.challenges.slice(0, 2).join(' y ')}</span>.
          </p>
        </div>
      </div>

      {/* Enhanced Key Demographics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 px-4">
        {/* Demographics */}
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-blue-200/50 hover:shadow-2xl transition-all duration-300">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-[#3018ef] to-blue-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-lg">👤</span>
            </div>
            <h3 className="text-[#101419] text-lg font-bold">Demografía</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Edad:</span>
              <span className="text-[#3018ef] font-bold">{persona.age} años</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Ubicación:</span>
              <span className="text-[#101419] font-semibold">{persona.location}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-purple-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Educación:</span>
              <span className="text-[#101419] font-semibold">{persona.education}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Ingresos:</span>
              <span className="text-green-700 font-bold">{persona.income_level}</span>
            </div>
          </div>
        </div>

        {/* Professional Info */}
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-purple-200/50 hover:shadow-2xl transition-all duration-300">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-[#dd3a5a] to-pink-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-lg">💼</span>
            </div>
            <h3 className="text-[#101419] text-lg font-bold">Información Profesional</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-pink-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Cargo:</span>
              <span className="text-[#dd3a5a] font-bold">{persona.job.title}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Empresa:</span>
              <span className="text-[#101419] font-semibold">{persona.job.company_size}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Industria:</span>
              <span className="text-[#3018ef] font-bold">{persona.job.industry}</span>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-green-200/50 hover:shadow-2xl transition-all duration-300">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-lg">📊</span>
            </div>
            <h3 className="text-[#101419] text-lg font-bold">Estadísticas Rápidas</h3>
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Objetivos:</span>
              <span className="text-green-700 font-bold text-xl">{persona.goals.length}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Desafíos:</span>
              <span className="text-red-600 font-bold text-xl">{persona.challenges.length}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-blue-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Canales:</span>
              <span className="text-[#3018ef] font-bold text-xl">{persona.communication_channels.length}</span>
            </div>
            <div className="flex justify-between items-center p-3 bg-orange-50 rounded-lg">
              <span className="text-[#57738e] font-medium">Objeciones:</span>
              <span className="text-orange-600 font-bold text-xl">{persona.objections.length}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Goals and Challenges Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 px-4">
        {/* Key Goals */}
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-green-200/50">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-[#3018ef] to-blue-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-xl">🎯</span>
            </div>
            <h3 className="text-[#101419] text-xl font-bold">Objetivos Principales</h3>
          </div>
          <div className="space-y-4">
            {persona.goals.map((goal, index) => (
              <div key={index} className="flex items-start gap-4 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-xl border-l-4 border-[#3018ef] hover:shadow-lg transition-all duration-300">
                <div className="w-8 h-8 bg-gradient-to-r from-[#3018ef] to-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm font-bold">{index + 1}</span>
                </div>
                <span className="text-[#101419] font-medium leading-relaxed">{goal}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Key Challenges */}
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-red-200/50">
          <div className="flex items-center gap-3 mb-6">
            <div className="w-12 h-12 bg-gradient-to-r from-[#dd3a5a] to-red-500 rounded-xl flex items-center justify-center">
              <span className="text-white text-xl">⚠️</span>
            </div>
            <h3 className="text-[#101419] text-xl font-bold">Principales Desafíos</h3>
          </div>
          <div className="space-y-4">
            {persona.challenges.map((challenge, index) => (
              <div key={index} className="flex items-start gap-4 p-4 bg-gradient-to-r from-red-50 to-orange-50 rounded-xl border-l-4 border-[#dd3a5a] hover:shadow-lg transition-all duration-300">
                <div className="w-8 h-8 bg-gradient-to-r from-[#dd3a5a] to-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                  <span className="text-white text-sm font-bold">!</span>
                </div>
                <span className="text-[#101419] font-medium leading-relaxed">{challenge}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Professional Responsibilities */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Responsabilidades Profesionales</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <ul className="space-y-2">
            {persona.job.responsibilities.map((responsibility, index) => (
              <li key={index} className="flex items-start gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-[#101419]">{responsibility}</span>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {/* Enhanced Buying Process */}
      <div className="px-4">
        <div className="bg-white/95 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-purple-200/50">
          <div className="flex items-center gap-3 mb-8">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-[#dd3a5a] rounded-xl flex items-center justify-center">
              <span className="text-white text-xl">🛒</span>
            </div>
            <h3 className="text-[#101419] text-2xl font-bold">Proceso de Compra</h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Research Methods */}
            <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-6 border-2 border-green-200/50">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                  <span className="text-white text-lg">🔍</span>
                </div>
                <h4 className="text-[#101419] font-bold text-lg">Métodos de Investigación</h4>
              </div>
              <div className="space-y-3">
                {persona.buying_process.research_methods.map((method, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-white/80 rounded-lg shadow-sm">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-xs font-bold">{index + 1}</span>
                    </div>
                    <span className="text-[#101419] font-medium">{method}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Decision Factors */}
            <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 border-2 border-orange-200/50">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                  <span className="text-white text-lg">⚖️</span>
                </div>
                <h4 className="text-[#101419] font-bold text-lg">Factores de Decisión</h4>
              </div>
              <div className="space-y-3">
                {persona.buying_process.decision_factors.map((factor, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-white/80 rounded-lg shadow-sm">
                    <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-xs font-bold">★</span>
                    </div>
                    <span className="text-[#101419] font-medium">{factor}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Timeline */}
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-6 border-2 border-blue-200/50">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-10 h-10 bg-gradient-to-r from-[#3018ef] to-purple-500 rounded-xl flex items-center justify-center">
                  <span className="text-white text-lg">⏰</span>
                </div>
                <h4 className="text-[#101419] font-bold text-lg">Timeline de Decisión</h4>
              </div>
              <div className="bg-gradient-to-r from-[#3018ef] to-purple-500 p-6 rounded-xl text-center">
                <span className="text-white font-bold text-xl">{persona.buying_process.timeline}</span>
                <p className="text-blue-100 text-sm mt-2">Tiempo promedio de decisión</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Objections */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Objeciones Comunes</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {persona.objections.map((objection, index) => (
              <div key={index} className="bg-red-50 p-3 rounded-lg border border-red-200">
                <span className="text-red-800 text-sm">{objection}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Communication Channels */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Canales de Comunicación Preferidos</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex flex-wrap gap-3">
            {persona.communication_channels.map((channel, index) => (
              <div key={index} className="bg-blue-100 text-blue-800 px-3 py-2 rounded-full text-sm font-medium">
                {channel}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Influences */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Influencias y Referencias</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {persona.influences.map((influence, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full mt-2 flex-shrink-0" />
                <span className="text-[#101419] text-sm">{influence}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Brand Affinities */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Marcas Preferidas</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="flex flex-wrap gap-3">
            {persona.brand_affinities.map((brand, index) => (
              <div key={index} className="bg-indigo-100 text-indigo-800 px-3 py-2 rounded-full text-sm font-medium border border-indigo-300">
                {brand}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Typical Day */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Un Día Típico</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-[#101419] leading-relaxed">{persona.typical_day}</p>
          </div>
        </div>
      </div>

      {/* Quotes */}
      {persona.quotes && persona.quotes.length > 0 && (
        <div className="px-4">
          <h3 className="text-[#101419] text-lg font-semibold mb-4">Frases Típicas</h3>
          <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
            <div className="space-y-4">
              {persona.quotes.map((quote, index) => (
                <div key={index} className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                  <blockquote className="text-[#101419] italic">
                    "{quote}"
                  </blockquote>
                  <cite className="text-[#57738e] text-sm mt-2 block">- {persona.name}</cite>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Additional Demographics */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Información Demográfica Adicional</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-[#57738e]">Género:</span>
                <span className="text-[#101419] font-medium">{persona.gender}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-[#57738e]">Estado Civil:</span>
                <span className="text-[#101419] font-medium">{persona.marital_status}</span>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-[#57738e]">Tamaño de Empresa:</span>
                <span className="text-[#101419] font-medium">{persona.job.company_size}</span>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg">
                <p className="text-[#57738e] text-sm mb-1">Descripción para Avatar:</p>
                <p className="text-[#101419] text-sm">{persona.avatar_description}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
