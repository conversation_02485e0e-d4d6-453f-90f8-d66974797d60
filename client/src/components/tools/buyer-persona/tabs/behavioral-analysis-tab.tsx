/**
 * Behavioral Analysis tab - Psychological and emotional analysis
 */

import { BuyerPersona, PremiumFeatureData } from "../../buyer-persona-generator/types";

interface BehavioralAnalysisTabProps {
  persona: BuyerPersona;
  premiumData?: PremiumFeatureData | null;
}

export function BehavioralAnalysisTab({ persona, premiumData }: BehavioralAnalysisTabProps) {
  return (
    <div className="space-y-6">
      {/* Section Title */}
      <h2 className="text-[#101419] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Análisis Conductual
      </h2>

      {/* Behavioral Overview */}
      <p className="text-[#101419] text-base font-normal leading-normal pb-3 pt-1 px-4">
        {persona.name} exhibe un enfoque {persona.buying_process.timeline.includes('semana') ? 'metódico y analítico' : 'ágil y decisivo'} 
        en su trabajo. Valora {persona.buying_process.decision_factors.slice(0, 2).join(' y ')}, y busca soluciones que le permitan 
        optimizar su rendimiento. Su análisis emocional revela una fuerte orientación hacia {persona.goals[0]?.toLowerCase()} 
        y una comunicación {persona.communication_channels.includes('Email') ? 'profesional y estructurada' : 'directa y eficiente'}.
      </p>

      {/* Communication Style */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Estilo de Comunicación</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Canales Preferidos</h4>
              <ul className="space-y-2">
                {persona.communication_channels.map((channel, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <span className="text-[#101419]">{channel}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Frases Típicas</h4>
              <div className="space-y-2">
                {persona.quotes.map((quote, index) => (
                  <div key={index} className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-[#57738e] italic">"{quote}"</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Decision Making Process */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Proceso de Toma de Decisiones</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Métodos de Investigación</h4>
              <ul className="space-y-2">
                {persona.buying_process.research_methods.map((method, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full" />
                    <span className="text-[#101419] text-sm">{method}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Factores de Decisión</h4>
              <ul className="space-y-2">
                {persona.buying_process.decision_factors.map((factor, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-purple-500 rounded-full" />
                    <span className="text-[#101419] text-sm">{factor}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Timeline</h4>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-[#101419] font-medium">{persona.buying_process.timeline}</p>
                <p className="text-[#57738e] text-sm mt-1">Tiempo típico de decisión</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Influences and Motivators */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Influencias y Motivadores</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Principales Influencias</h4>
              <div className="space-y-3">
                {persona.influences.map((influence, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                      <span className="text-orange-600 font-semibold text-sm">{index + 1}</span>
                    </div>
                    <span className="text-[#101419]">{influence}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Marcas de Afinidad</h4>
              <div className="flex flex-wrap gap-2">
                {persona.brand_affinities.map((brand, index) => (
                  <span 
                    key={index} 
                    className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                  >
                    {brand}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Premium Emotional Analysis */}
      {premiumData?.behavior?.predictions?.emotional_analysis && (
        <div className="px-4">
          <h3 className="text-[#101419] text-lg font-semibold mb-4">Análisis Emocional Avanzado</h3>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-purple-500 rounded-full" />
              <span className="text-purple-800 font-semibold">Análisis Premium</span>
            </div>
            <p className="text-[#101419]">
              Análisis emocional detallado disponible con datos premium.
            </p>
          </div>
        </div>
      )}

      {/* Typical Day */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Un Día Típico</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <p className="text-[#101419] leading-relaxed">{persona.typical_day}</p>
        </div>
      </div>
    </div>
  );
}
