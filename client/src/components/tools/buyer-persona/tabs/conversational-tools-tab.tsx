/**
 * Conversational Tools tab - Simulators and AI tools
 */

import { BuyerPersona, PremiumFeatureData } from "../../buyer-persona-generator/types";

interface ConversationalToolsTabProps {
  persona: BuyerPersona;
  premiumData?: PremiumFeatureData | null;
  onOpenConversationSimulator?: () => void;
}

export function ConversationalToolsTab({ 
  persona, 
  premiumData, 
  onOpenConversationSimulator 
}: ConversationalToolsTabProps) {
  return (
    <div className="space-y-6">
      {/* Section Title */}
      <h2 className="text-[#101419] text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">
        Herramientas de Conversación
      </h2>

      {/* Main Simulator Button */}
      <div className="flex px-4 py-3 justify-start">
        <button
          onClick={onOpenConversationSimulator}
          className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-xl h-10 px-4 bg-[#327fcc] text-gray-50 text-sm font-bold leading-normal tracking-[0.015em] hover:bg-[#2968a3] transition-colors"
        >
          <span className="truncate">Lanzar Simulador de Conversación</span>
        </button>
      </div>

      {/* Conversation Preview */}
      {premiumData?.conversation && (
        <div className="px-4">
          <h3 className="text-[#101419] text-lg font-semibold mb-4">Vista Previa de Conversación</h3>
          <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-6">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                <span className="text-white text-sm">💬</span>
              </div>
              <h4 className="text-green-800 font-semibold">Conversación con {persona.name}</h4>
            </div>
            <div className="bg-white rounded-lg p-4">
              <div className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">👤</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-[#101419] text-sm">
                      "Hola, estoy interesado en conocer más sobre sus soluciones para {persona.job.industry}..."
                    </p>
                    <span className="text-[#57738e] text-xs">Mensaje inicial típico</span>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white text-xs">🤖</span>
                  </div>
                  <div className="flex-1">
                    <p className="text-[#101419] text-sm">
                      "Perfecto, {persona.name}. Entiendo que como {persona.job.title} buscas {persona.goals[0]?.toLowerCase()}..."
                    </p>
                    <span className="text-[#57738e] text-xs">Respuesta personalizada</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Conversation Guidelines */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Guías de Conversación</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Temas de Apertura</h4>
              <div className="space-y-3">
                {persona.goals.slice(0, 3).map((goal, index) => (
                  <div key={index} className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-blue-800 font-medium">💡 {goal}</p>
                    <p className="text-blue-600 text-sm">
                      "¿Cómo está abordando actualmente {goal.toLowerCase()}?"
                    </p>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Puntos de Dolor a Explorar</h4>
              <div className="space-y-3">
                {persona.challenges.slice(0, 3).map((challenge, index) => (
                  <div key={index} className="p-3 bg-red-50 rounded-lg">
                    <p className="text-red-800 font-medium">⚠️ {challenge}</p>
                    <p className="text-red-600 text-sm">
                      "¿Qué impacto tiene {challenge.toLowerCase()} en su operación?"
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Response Templates */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Plantillas de Respuesta</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="space-y-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="text-[#101419] font-semibold mb-2">Respuesta a Objeciones de Precio</h4>
              <div className="bg-gray-50 p-3 rounded">
                <p className="text-[#101419] text-sm">
                  "Entiendo su preocupación sobre el precio, {persona.name}. Permíteme mostrarte cómo otros 
                  {persona.job.title}s en {persona.job.industry} han visto un ROI del 300% en los primeros 6 meses..."
                </p>
              </div>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="text-[#101419] font-semibold mb-2">Respuesta a Dudas sobre Implementación</h4>
              <div className="bg-gray-50 p-3 rounded">
                <p className="text-[#101419] text-sm">
                  "Es una excelente pregunta. Nuestro equipo de implementación ha trabajado con empresas {persona.job.company_size.toLowerCase()}s 
                  como la suya, y típicamente el proceso toma {persona.buying_process.timeline}..."
                </p>
              </div>
            </div>
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="text-[#101419] font-semibold mb-2">Cierre de Reunión</h4>
              <div className="bg-gray-50 p-3 rounded">
                <p className="text-[#101419] text-sm">
                  "Basándome en lo que me has contado sobre {persona.challenges[0]?.toLowerCase()}, 
                  creo que podemos ayudarte a {persona.goals[0]?.toLowerCase()}. ¿Te parece si programamos una demo 
                  personalizada para la próxima semana?"
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Communication Style Guide */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Guía de Estilo de Comunicación</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Tono Recomendado</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-[#101419] text-sm">Profesional</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-[#101419] text-sm">Directo</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <span className="text-[#101419] text-sm">Orientado a resultados</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Palabras Clave</h4>
              <div className="flex flex-wrap gap-2">
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Eficiencia</span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">ROI</span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Optimización</span>
                <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">Resultados</span>
              </div>
            </div>
            <div>
              <h4 className="text-[#101419] font-semibold mb-3">Evitar</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                  <span className="text-[#101419] text-sm">Jerga técnica excesiva</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                  <span className="text-[#101419] text-sm">Promesas vagas</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                  <span className="text-[#101419] text-sm">Presión de venta</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Conversation Analytics */}
      {premiumData?.conversation?.analytics && (
        <div className="px-4">
          <h3 className="text-[#101419] text-lg font-semibold mb-4">Análisis de Conversación</h3>
          <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-6 border border-purple-200">
            <div className="flex items-center gap-2 mb-4">
              <div className="w-3 h-3 bg-purple-500 rounded-full" />
              <span className="text-purple-800 font-semibold">Análisis Premium Disponible</span>
            </div>
            <p className="text-[#101419] mb-4">
              Métricas avanzadas de conversación y análisis de sentimiento están disponibles con el análisis premium.
            </p>
            <div className="bg-white rounded-lg p-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <p className="text-2xl font-bold text-purple-600">85%</p>
                  <p className="text-[#57738e] text-sm">Engagement Score</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-green-600">92%</p>
                  <p className="text-[#57738e] text-sm">Sentiment Positivo</p>
                </div>
                <div>
                  <p className="text-2xl font-bold text-blue-600">7.2</p>
                  <p className="text-[#57738e] text-sm">Interacciones Promedio</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="px-4">
        <h3 className="text-[#101419] text-lg font-semibold mb-4">Acciones Rápidas</h3>
        <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-blue-600">📧</span>
              </div>
              <div className="text-left">
                <p className="text-[#101419] font-medium">Generar Email de Seguimiento</p>
                <p className="text-[#57738e] text-sm">Personalizado para {persona.name}</p>
              </div>
            </button>
            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <span className="text-green-600">📞</span>
              </div>
              <div className="text-left">
                <p className="text-[#101419] font-medium">Script de Llamada</p>
                <p className="text-[#57738e] text-sm">Adaptado a sus necesidades</p>
              </div>
            </button>
            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                <span className="text-purple-600">📊</span>
              </div>
              <div className="text-left">
                <p className="text-[#101419] font-medium">Propuesta Personalizada</p>
                <p className="text-[#57738e] text-sm">Con datos específicos</p>
              </div>
            </button>
            <button className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                <span className="text-orange-600">🎯</span>
              </div>
              <div className="text-left">
                <p className="text-[#101419] font-medium">Plan de Seguimiento</p>
                <p className="text-[#57738e] text-sm">Timeline personalizado</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
