"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { User, Briefcase, MapPin, DollarSign, Target, AlertTriangle, TrendingUp, Clock } from "lucide-react";

interface JobInfo {
  title: string;
  company_size: string;
  industry: string;
  responsibilities: string[];
}

interface BuyingProcess {
  research_methods: string[];
  decision_factors: string[];
  timeline: string;
}

interface BuyerPersona {
  name: string;
  age: number;
  gender: string;
  location: string;
  education: string;
  income_level: string;
  marital_status: string;
  job: JobInfo;
  personal_background: string;
  goals: string[];
  challenges: string[];
  buying_process: BuyingProcess;
  objections: string[];
  communication_channels: string[];
  influences: string[];
  quotes: string[];
  typical_day: string;
  brand_affinities: string[];
  avatar_description: string;

  // Avatar fields (optional, generated automatically)
  avatar_url?: string;
  avatar_id?: string;
}

interface PersonaSummaryProps {
  persona: BuyerPersona;
}

export function PersonaSummary({ persona }: PersonaSummaryProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 }}
      className="mb-8"
    >
      <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
        <CardHeader className="text-center pb-4">
          <CardTitle className="text-2xl font-bold text-gray-800 mb-2">
            📊 Resumen Ejecutivo: {persona.name}
          </CardTitle>
          <p className="text-gray-600">Vista rápida de los insights más importantes</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Demographics */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <User className="h-5 w-5 text-blue-600" />
                <h4 className="font-semibold text-gray-800">Demografía</h4>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">Edad:</span>
                  <span className="font-medium">{persona.age} años</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-700">{persona.location}</span>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-700">{persona.income_level}</span>
                </div>
              </div>
            </div>

            {/* Professional */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <Briefcase className="h-5 w-5 text-green-600" />
                <h4 className="font-semibold text-gray-800">Profesional</h4>
              </div>
              <div className="space-y-2 text-sm">
                <div>
                  <span className="font-medium text-gray-800">{persona.job.title}</span>
                </div>
                <div className="text-gray-600">{persona.job.industry}</div>
                <Badge variant="outline" className="text-xs">
                  {persona.job.company_size}
                </Badge>
              </div>
            </div>

            {/* Goals & Challenges */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <Target className="h-5 w-5 text-purple-600" />
                <h4 className="font-semibold text-gray-800">Motivaciones</h4>
              </div>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="text-green-700 font-medium">Top Goal:</span>
                  <p className="text-gray-700 text-xs mt-1">{persona.goals[0]}</p>
                </div>
                <div>
                  <span className="text-red-700 font-medium">Main Challenge:</span>
                  <p className="text-gray-700 text-xs mt-1">{persona.challenges[0]}</p>
                </div>
              </div>
            </div>

            {/* Buying Process */}
            <div className="bg-white rounded-lg p-4 shadow-sm">
              <div className="flex items-center gap-2 mb-3">
                <TrendingUp className="h-5 w-5 text-orange-600" />
                <h4 className="font-semibold text-gray-800">Compra</h4>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-700">{persona.buying_process.timeline}</span>
                </div>
                <div>
                  <span className="text-gray-600">Canal principal:</span>
                  <p className="text-gray-800 font-medium text-xs">{persona.communication_channels[0]}</p>
                </div>
                <div>
                  <span className="text-gray-600">Factor clave:</span>
                  <p className="text-gray-800 font-medium text-xs">{persona.buying_process.decision_factors[0]}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Key Quote */}
          {persona.quotes && persona.quotes.length > 0 && (
            <div className="mt-6 bg-white rounded-lg p-4 border-l-4 border-blue-500">
              <blockquote className="text-gray-700 italic text-center">
                "{persona.quotes[0]}"
              </blockquote>
              <cite className="text-sm text-gray-500 text-center block mt-2">- {persona.name}</cite>
            </div>
          )}

          {/* Quick Actions */}
          <div className="mt-6 flex flex-wrap gap-3 justify-center">
            <Badge className="bg-blue-100 text-blue-800 px-3 py-1">
              🎯 {persona.goals.length} objetivos identificados
            </Badge>
            <Badge className="bg-red-100 text-red-800 px-3 py-1">
              ⚠️ {persona.challenges.length} desafíos clave
            </Badge>
            <Badge className="bg-yellow-100 text-yellow-800 px-3 py-1">
              💬 {persona.objections.length} objeciones comunes
            </Badge>
            <Badge className="bg-green-100 text-green-800 px-3 py-1">
              📱 {persona.communication_channels.length} canales preferidos
            </Badge>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
