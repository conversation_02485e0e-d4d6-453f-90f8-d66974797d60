/**
 * Modern tabs-based view for buyer persona results
 * Inspired by professional SaaS applications
 */

import { useState } from "react";
import { motion } from "framer-motion";
import { BuyerPersona, PremiumFeatureData } from "../buyer-persona-generator/types";
import { OverviewTab } from "./tabs/overview-tab";
import { BehavioralAnalysisTab } from "./tabs/behavioral-analysis-tab";
import { LeadScoringTab } from "./tabs/lead-scoring-tab";
import { TimingFollowupTab } from "./tabs/timing-followup-tab";
import { SalesContentTab } from "./tabs/sales-content-tab";
import { ConversationalToolsTab } from "./tabs/conversational-tools-tab";

interface PersonaTabsViewProps {
  persona: BuyerPersona;
  premiumData?: PremiumFeatureData | null;
  onOpenConversationSimulator?: () => void;
}

type TabType = 'overview' | 'behavioral' | 'lead-scoring' | 'timing' | 'sales-content' | 'conversational';

const tabs = [
  { id: 'overview', label: 'Resumen General' },
  { id: 'behavioral', label: 'Análisis Conductual' },
  { id: 'lead-scoring', label: 'Puntuación y Recomendaciones' },
  { id: 'timing', label: 'Timing y Seguimiento' },
  { id: 'sales-content', label: 'Estrategia de Ventas' },
  { id: 'conversational', label: 'Herramientas de Conversación' },
] as const;

export function PersonaTabsView({ 
  persona, 
  premiumData, 
  onOpenConversationSimulator 
}: PersonaTabsViewProps) {
  const [activeTab, setActiveTab] = useState<TabType>('overview');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <OverviewTab persona={persona} premiumData={premiumData} />;
      case 'behavioral':
        return <BehavioralAnalysisTab persona={persona} premiumData={premiumData} />;
      case 'lead-scoring':
        return <LeadScoringTab persona={persona} premiumData={premiumData} />;
      case 'timing':
        return <TimingFollowupTab persona={persona} premiumData={premiumData} />;
      case 'sales-content':
        return <SalesContentTab persona={persona} premiumData={premiumData} />;
      case 'conversational':
        return <ConversationalToolsTab
          persona={persona}
          premiumData={premiumData}
          onOpenConversationSimulator={onOpenConversationSimulator}
        />;
      default:
        return <OverviewTab persona={persona} premiumData={premiumData} />;
    }
  };

  return (
    <div className="bg-gradient-to-br from-blue-50/30 to-purple-50/30 rounded-2xl" style={{ fontFamily: 'Inter, "Noto Sans", sans-serif' }}>
      <div className="layout-content-container flex flex-col max-w-full">
        {/* Enhanced Page Title */}
        <div className="bg-gradient-to-r from-[#3018ef] to-[#dd3a5a] p-6 rounded-t-2xl">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center">
              <span className="text-white text-xl">👤</span>
            </div>
            <div>
              <h1 className="text-white text-[28px] font-bold leading-tight tracking-[-0.015em]">
                {persona.name}
              </h1>
              <p className="text-blue-100 text-lg font-medium">
                {persona.job.title} • {persona.age} años • {persona.location}
              </p>
            </div>
          </div>
        </div>

        {/* Enhanced Tabs Navigation */}
        <div className="bg-white/95 backdrop-blur-xl border-b border-purple-200/50">
          <div className="flex px-6 gap-2 overflow-x-auto">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as TabType)}
                className={`flex items-center gap-3 px-6 py-4 rounded-t-xl border-b-4 transition-all duration-300 whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-b-[#3018ef] bg-gradient-to-t from-blue-50 to-white text-[#3018ef] shadow-lg'
                    : 'border-b-transparent text-[#57738e] hover:text-[#3018ef] hover:bg-blue-50/50'
                }`}
              >
                <span className="text-lg">
                  {tab.id === 'overview' && '📊'}
                  {tab.id === 'behavioral' && '🧠'}
                  {tab.id === 'lead-scoring' && '⭐'}
                  {tab.id === 'timing' && '⏰'}
                  {tab.id === 'sales-content' && '📝'}
                  {tab.id === 'conversational' && '💬'}
                </span>
                <span className={`text-sm font-bold leading-normal tracking-[0.015em] ${
                  activeTab === tab.id ? 'text-[#3018ef]' : 'text-[#57738e]'
                }`}>
                  {tab.label}
                </span>
                {activeTab === tab.id && (
                  <div className="w-2 h-2 bg-[#dd3a5a] rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Enhanced Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
          className="flex-1 bg-white/95 backdrop-blur-xl rounded-b-2xl min-h-[600px]"
        >
          {renderTabContent()}
        </motion.div>
      </div>
    </div>
  );
}
