"use client";

import { useState, useEffect, useCallback } from "react";

export interface SEOProgressData {
  current_page: number;
  total_pages: number;
  current_url?: string;
  status: string;
  processed_urls: string[];
  failed_urls: string[];
  estimated_time_remaining?: number;
  start_time?: number;
  phase: 'discovery' | 'analysis' | 'recommendations' | 'complete';
  error_details?: Array<{
    url: string;
    error: string;
    timestamp: number;
  }>;
}

interface UseSEOProgressOptions {
  analysisMode: "page" | "website";
  url: string;
  enabled: boolean;
  onComplete?: (data: any) => void;
  onError?: (error: string) => void;
}

export function useSEOProgress({
  analysisMode,
  url,
  enabled,
  onComplete,
  onError
}: UseSEOProgressOptions) {
  const [progress, setProgress] = useState<SEOProgressData>({
    current_page: 0,
    total_pages: 0,
    status: "Preparando análisis...",
    processed_urls: [],
    failed_urls: [],
    phase: 'discovery'
  });

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Función para obtener el progreso del backend
  const fetchProgress = useCallback(async (analysisId: string) => {
    try {
      const response = await fetch(`/api/seo/progress/${analysisId}`);
      if (!response.ok) {
        throw new Error('Error al obtener progreso');
      }

      const data = await response.json();
      const progressData = data.progress || data;

      setProgress(progressData);

      // Si el análisis está completo, llamar onComplete
      if (data.status === 'complete' || progressData.phase === 'complete') {
        setIsLoading(false);
        onComplete?.(data.result || progressData);
      }

      return progressData;
    } catch (err) {
      console.error('Error fetching progress:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
      setIsLoading(false);
      onError?.(err instanceof Error ? err.message : 'Error desconocido');
    }
  }, [onComplete, onError]);

  // Función para iniciar el análisis
  const startAnalysis = useCallback(async () => {
    if (!enabled || !url) return;

    setIsLoading(true);
    setError(null);
    setProgress({
      current_page: 0,
      total_pages: 0,
      status: "Iniciando análisis...",
      processed_urls: [],
      failed_urls: [],
      phase: 'discovery'
    });

    try {
      // Iniciar el análisis en el backend
      const endpoint = analysisMode === "website" ? "/api/seo/analyze-website" : "/api/seo/analyze";
      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ 
          url, 
          mode: analysisMode,
          enable_progress: true // Habilitar seguimiento de progreso
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error_message || "Error al iniciar análisis");
      }

      const result = await response.json();
      
      // Si el análisis es síncrono (página única), completar inmediatamente
      if (analysisMode === "page" && result.status === "success") {
        setProgress(prev => ({
          ...prev,
          phase: 'complete',
          status: "Análisis completado"
        }));
        setIsLoading(false);
        onComplete?.(result);
        return;
      }

      // Para análisis de sitio web, obtener el ID de análisis y hacer polling
      const analysisId = result.analysis_id;
      if (analysisId && result.status === "started") {
        console.log(`Análisis exhaustivo iniciado con ID: ${analysisId}`);

        // Hacer polling cada 3 segundos para obtener progreso
        const pollInterval = setInterval(async () => {
          try {
            const progressData = await fetchProgress(analysisId);
            if (progressData?.phase === 'complete') {
              clearInterval(pollInterval);
            } else if (progressData?.phase === 'error') {
              clearInterval(pollInterval);
              setError(progressData.error || "Error en el análisis");
              setIsLoading(false);
              onError?.(progressData.error || "Error en el análisis");
            }
          } catch (pollError) {
            console.error('Error polling progress:', pollError);
          }
        }, 3000);

        // Limpiar interval después de 60 minutos máximo
        setTimeout(() => {
          clearInterval(pollInterval);
          if (isLoading) {
            setError("Tiempo de espera agotado (60 minutos)");
            setIsLoading(false);
            onError?.("Tiempo de espera agotado");
          }
        }, 60 * 60 * 1000);
      } else {
        throw new Error("No se pudo iniciar el análisis exhaustivo");
      }

    } catch (err) {
      console.error('Error starting analysis:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
      setIsLoading(false);
      onError?.(err instanceof Error ? err.message : 'Error desconocido');
    }
  }, [enabled, url, analysisMode, fetchProgress, onComplete, onError, isLoading]);

  // Función para simular progreso (fallback cuando no hay backend real)
  const simulateProgress = useCallback(() => {
    if (!enabled) return;

    setIsLoading(true);
    setError(null);

    if (analysisMode === "website") {
      // Simular análisis de sitio web
      let currentPage = 0;
      const totalPages = Math.floor(Math.random() * 100) + 20;
      
      setProgress({
        current_page: 0,
        total_pages: totalPages,
        status: "Descubriendo páginas del sitio web...",
        processed_urls: [],
        failed_urls: [],
        phase: 'discovery'
      });

      const interval = setInterval(() => {
        setProgress(prev => {
          const newProgress = { ...prev };
          
          if (currentPage === 0) {
            // Fase de descubrimiento
            newProgress.phase = 'discovery';
            newProgress.status = "Extrayendo URLs del sitemap...";
            currentPage = 1;
          } else if (currentPage <= totalPages) {
            // Fase de análisis
            newProgress.phase = 'analysis';
            newProgress.current_page = currentPage;
            newProgress.current_url = `${url}/pagina-${currentPage}`;
            newProgress.status = `Analizando página ${currentPage}/${totalPages}`;
            
            // Agregar URL procesada
            if (newProgress.current_url) {
              newProgress.processed_urls = [...prev.processed_urls.slice(-4), newProgress.current_url];
            }
            
            // Simular errores ocasionales
            if (Math.random() < 0.03) {
              newProgress.failed_urls = [...prev.failed_urls, newProgress.current_url || ""];
            }
            
            // Calcular tiempo estimado
            const rate = 2; // páginas por segundo
            const remaining = (totalPages - currentPage) * rate;
            newProgress.estimated_time_remaining = Math.round(remaining);
            
            currentPage++;
          } else {
            // Fase final
            newProgress.phase = 'recommendations';
            newProgress.status = "Generando recomendaciones finales...";
            
            // Completar después de 3 segundos
            setTimeout(() => {
              setProgress(prev => ({
                ...prev,
                phase: 'complete',
                status: "Análisis completado"
              }));
              setIsLoading(false);
              clearInterval(interval);
              
              // Simular datos de resultado
              onComplete?.({
                status: "success",
                url: url,
                total_pages_analyzed: totalPages,
                recommendations: []
              });
            }, 3000);
          }
          
          return newProgress;
        });
      }, 2000);

      return () => clearInterval(interval);
    } else {
      // Simular análisis de página única
      const phases = [
        { status: "Conectando con el sitio web...", phase: 'discovery' as const },
        { status: "Analizando estructura HTML...", phase: 'analysis' as const },
        { status: "Evaluando meta tags...", phase: 'analysis' as const },
        { status: "Verificando enlaces...", phase: 'analysis' as const },
        { status: "Analizando contenido...", phase: 'analysis' as const },
        { status: "Generando recomendaciones...", phase: 'recommendations' as const }
      ];
      
      let phaseIndex = 0;
      const interval = setInterval(() => {
        if (phaseIndex < phases.length) {
          setProgress(prev => ({
            ...prev,
            status: phases[phaseIndex].status,
            phase: phases[phaseIndex].phase,
            current_page: phaseIndex + 1,
            total_pages: phases.length
          }));
          phaseIndex++;
        } else {
          setProgress(prev => ({
            ...prev,
            phase: 'complete',
            status: "Análisis completado"
          }));
          setIsLoading(false);
          clearInterval(interval);
          
          // Simular datos de resultado
          onComplete?.({
            status: "success",
            url: url,
            recommendations: []
          });
        }
      }, 3000);

      return () => clearInterval(interval);
    }
  }, [enabled, analysisMode, url, onComplete]);

  // Función para resetear el progreso
  const resetProgress = useCallback(() => {
    setProgress({
      current_page: 0,
      total_pages: 0,
      status: "Preparando análisis...",
      processed_urls: [],
      failed_urls: [],
      phase: 'discovery'
    });
    setIsLoading(false);
    setError(null);
  }, []);

  return {
    progress,
    isLoading,
    error,
    startAnalysis,
    simulateProgress,
    resetProgress
  };
}
