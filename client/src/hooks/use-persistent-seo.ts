import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";

interface PersistentSEOOptions {
  url: string;
  mode: "page" | "website";
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
}

interface AnalysisProgress {
  status: string;
  analysis_id: string;
  url: string;
  mode: string;
  phase?: string;
  current_page: number;
  total_pages: number;
  status_message?: string;
  created_at?: string;
  started_at?: string;
  completed_at?: string;
  estimated_completion?: string;
  processing_time?: number;
  pages_analyzed: number;
  total_pages_found: number;
  failed_urls_count: number;
  ai_enhanced: boolean;
  result?: any;
  error?: any;
}

export function usePersistentSEO(options: PersistentSEOOptions) {
  const [progress, setProgress] = useState<AnalysisProgress | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [analysisId, setAnalysisId] = useState<string | null>(null);
  const { toast } = useToast();

  // Start a new analysis
  const startAnalysis = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      setProgress(null);

      const response = await fetch("/api/seo/analyze-website", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          url: options.url,
          mode: options.mode,
          enable_progress: true,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to start analysis");
      }

      const data = await response.json();
      
      if (data.status === "started") {
        setAnalysisId(data.analysis_id);
        toast({
          title: "Análisis iniciado",
          description: "El análisis se ha iniciado. Puedes cerrar esta página y volver más tarde.",
        });
        return data.analysis_id;
      } else {
        throw new Error(data.error_message || "Failed to start analysis");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      options.onError?.(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [options, toast]);

  // Check progress of an existing analysis
  const checkProgress = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/seo/progress/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new Error("Analysis not found");
        }
        throw new Error("Failed to fetch progress");
      }

      const progressData = await response.json();
      setProgress(progressData);

      // Handle completion
      if (progressData.status === "complete" && progressData.result) {
        options.onComplete?.(progressData.result);
        toast({
          title: "Análisis completado",
          description: "El análisis SEO se ha completado exitosamente",
        });
      }

      // Handle errors
      if (progressData.status === "error") {
        const errorMessage = progressData.error?.error || "Analysis failed";
        setError(errorMessage);
        options.onError?.(errorMessage);
      }

      return progressData;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      setError(errorMessage);
      return null;
    }
  }, [options, toast]);

  // Cancel an analysis
  const cancelAnalysis = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/seo/analyses/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to cancel analysis");
      }

      toast({
        title: "Análisis cancelado",
        description: "El análisis ha sido cancelado exitosamente",
      });

      setProgress(null);
      setAnalysisId(null);
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    }
  }, [toast]);

  // Resume an existing analysis by ID
  const resumeAnalysis = useCallback((id: string) => {
    setAnalysisId(id);
    setError(null);
    setProgress(null);
  }, []);

  // Poll for progress updates
  useEffect(() => {
    if (!analysisId) return;

    const pollProgress = async () => {
      await checkProgress(analysisId);
    };

    // Initial check
    pollProgress();

    // Set up polling interval
    const interval = setInterval(pollProgress, 3000); // Poll every 3 seconds

    return () => clearInterval(interval);
  }, [analysisId, checkProgress]);

  // Auto-stop polling when analysis is complete or failed
  useEffect(() => {
    if (progress && ["complete", "error", "cancelled"].includes(progress.status)) {
      // Stop polling by clearing the analysis ID after a delay
      const timeout = setTimeout(() => {
        if (progress.status === "complete") {
          // Keep the analysis ID for completed analyses so user can view results
          return;
        }
        setAnalysisId(null);
      }, 5000);

      return () => clearTimeout(timeout);
    }
  }, [progress]);

  return {
    // State
    progress,
    isLoading,
    error,
    analysisId,
    
    // Actions
    startAnalysis,
    checkProgress,
    cancelAnalysis,
    resumeAnalysis,
    
    // Computed values
    isActive: progress && ["pending", "in_progress"].includes(progress.status),
    isComplete: progress?.status === "complete",
    isFailed: progress && ["error", "cancelled"].includes(progress.status),
    progressPercentage: progress && progress.total_pages > 0 
      ? Math.round((progress.current_page / progress.total_pages) * 100)
      : 0,
  };
}

// Hook for managing multiple analyses
export function useSEOAnalysesList() {
  const [analyses, setAnalyses] = useState<AnalysisProgress[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const fetchAnalyses = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/seo/analyses");
      
      if (!response.ok) {
        throw new Error("Failed to fetch analyses");
      }

      const data = await response.json();
      setAnalyses(data.analyses || []);
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to fetch analyses",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  const cancelAnalysis = useCallback(async (analysisId: string) => {
    try {
      const response = await fetch(`/api/seo/analyses/${analysisId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to cancel analysis");
      }

      toast({
        title: "Análisis cancelado",
        description: "El análisis ha sido cancelado exitosamente",
      });

      // Refresh the list
      await fetchAnalyses();
      return true;
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to cancel analysis",
        variant: "destructive",
      });
      return false;
    }
  }, [toast, fetchAnalyses]);

  // Auto-refresh every 10 seconds
  useEffect(() => {
    fetchAnalyses();
    const interval = setInterval(fetchAnalyses, 10000);
    return () => clearInterval(interval);
  }, [fetchAnalyses]);

  return {
    analyses,
    isLoading,
    fetchAnalyses,
    cancelAnalysis,
    activeAnalyses: analyses.filter(a => ["pending", "in_progress"].includes(a.status)),
    completedAnalyses: analyses.filter(a => a.status === "complete"),
    failedAnalyses: analyses.filter(a => ["error", "cancelled"].includes(a.status)),
  };
}
