import { useState, useEffect } from 'react';

const SAVED_IMAGE_TEXT_KEY = 'emma-studio-saved-image-text';

export interface SavedImageText {
  id: string;
  image_url: string;
  prompt: string;
  response_id?: string;
  metadata?: any;
  timestamp: number;
  type: 'generation' | 'reference' | 'edit';
}

export interface ImageTextData {
  image_url: string;
  prompt: string;
  response_id?: string;
  metadata?: any;
  type?: 'generation' | 'reference' | 'edit';
}

export function useSavedImageText() {
  const [savedImageTexts, setSavedImageTexts] = useState<SavedImageText[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Cargar imágenes guardadas del localStorage
  useEffect(() => {
    try {
      const saved = localStorage.getItem(SAVED_IMAGE_TEXT_KEY);
      if (saved) {
        const parsedSaved = JSON.parse(saved);
        setSavedImageTexts(parsedSaved);
      }
    } catch (error) {
      console.error('Error loading saved image texts:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Guardar en localStorage cuando cambie el estado
  useEffect(() => {
    if (!isLoading) {
      try {
        localStorage.setItem(SAVED_IMAGE_TEXT_KEY, JSON.stringify(savedImageTexts));
      } catch (error) {
        console.error('Error saving image texts to localStorage:', error);
      }
    }
  }, [savedImageTexts, isLoading]);

  // Crear una nueva imagen guardada
  const createSavedImageText = (data: ImageTextData): SavedImageText => {
    return {
      id: `img_text_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      image_url: data.image_url,
      prompt: data.prompt,
      response_id: data.response_id,
      metadata: data.metadata,
      timestamp: Date.now(),
      type: data.type || 'generation',
    };
  };

  // Agregar imagen a favoritos
  const addToSaved = (data: ImageTextData) => {
    const newImageText = createSavedImageText(data);
    setSavedImageTexts(prev => [newImageText, ...prev].slice(0, 50)); // Limitar a 50
    return newImageText;
  };

  // Remover imagen de favoritos
  const removeFromSaved = (id: string) => {
    setSavedImageTexts(prev => prev.filter(item => item.id !== id));
  };

  // Verificar si una imagen está guardada
  const isImageTextSaved = (imageUrl: string): boolean => {
    return savedImageTexts.some(item => item.image_url === imageUrl);
  };

  // Obtener imagen guardada por URL
  const getSavedImageText = (imageUrl: string): SavedImageText | undefined => {
    return savedImageTexts.find(item => item.image_url === imageUrl);
  };

  // Limpiar todas las imágenes guardadas
  const clearSaved = () => {
    setSavedImageTexts([]);
  };

  return {
    savedImageTexts,
    setSavedImageTexts,
    isLoading,
    createSavedImageText,
    addToSaved,
    removeFromSaved,
    isImageTextSaved,
    getSavedImageText,
    clearSaved,
  };
}
