import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Boxes,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  Upload,
  Sparkles,
  FileImage,
  Trash2,
  Settings,
  Zap,
  Info,
  Loader2,
} from "lucide-react";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  generate3DModel,
  downloadGLBFile,
  validateImageFor3D,
  formatFileSize,
  Generate3DOptions,
  Generate3DResponse
} from "@/services/stability-3d-service";

// Tipos para el estado de la aplicación
interface Generated3DModel {
  id: string;
  model_url: string;
  filename: string;
  size_mb?: number;
  prompt: string;
  metadata?: any;
  timestamp: number;
}

// Interfaz para modelos 3D guardados
interface Saved3DModel {
  id: string;
  model_url: string;
  filename: string;
  size_mb?: number;
  prompt: string;
  metadata?: any;
  timestamp: number;
  type: "fast" | "point_aware";
}

// Custom hook para localStorage
const useLocalStorage = <T,>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void] => {
  const [storedValue, setStoredValue] = useState<T>(() => {
    if (typeof window === "undefined") {
      return initialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.log(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      if (typeof window !== "undefined") {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
        window.dispatchEvent(new CustomEvent('localStorage', {
          detail: { key, newValue: valueToStore }
        }));
      }
    } catch (error) {
      console.log(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue];
};

// Utilidades para modelos 3D guardados
const SAVED_3D_MODELS_KEY = "emma-saved-3d-models";

function createSaved3DModel(data: {
  model_url: string;
  filename: string;
  size_mb?: number;
  prompt: string;
  metadata?: any;
  type: "fast" | "point_aware";
}): Saved3DModel {
  return {
    id: `3d-model-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: Date.now(),
    ...data,
  };
}

function is3DModelSaved(modelUrl: string, saved3DModels: Saved3DModel[]): boolean {
  return saved3DModels.some(model => model.model_url === modelUrl);
}

export default function Generate3DPage() {
  // Estados principales
  const [selectedImage, setSelectedImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [progressMessage, setProgressMessage] = useState("");
  const [current3DModel, setCurrent3DModel] = useState<Generated3DModel | null>(null);

  // Estados de configuración
  const [modelType, setModelType] = useState<'fast' | 'point_aware'>('fast');
  const [textureResolution, setTextureResolution] = useState<'512' | '1024' | '2048'>('1024');
  const [foregroundRatio, setForegroundRatio] = useState([0.85]);
  const [remesh, setRemesh] = useState<'none' | 'triangle' | 'quad'>('none');
  const [vertexCount, setVertexCount] = useState<number>(-1);
  // Point Aware 3D específicos
  const [targetType, setTargetType] = useState<'none' | 'vertex' | 'face'>('none');
  const [targetCount, setTargetCount] = useState<number>(1000);
  const [guidanceScale, setGuidanceScale] = useState([3.0]);
  const [seed, setSeed] = useState<number>(0);

  // Estados para favoritos
  const [saved3DModels, setSaved3DModels] = useLocalStorage<Saved3DModel[]>(SAVED_3D_MODELS_KEY, []);
  const [current3DModelSaved, setCurrent3DModelSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Cambiar modelo y ajustar valores por defecto
  const handleModelTypeChange = useCallback((newModelType: 'fast' | 'point_aware') => {
    setModelType(newModelType);

    if (newModelType === 'point_aware') {
      // Point Aware 3D defaults
      setForegroundRatio([1.3]);
      setGuidanceScale([3.0]);
      setTargetCount(1000);
      // Generar seed aleatorio automáticamente para Point Aware 3D
      setSeed(Math.floor(Math.random() * 4294967294));
    } else {
      // Fast 3D defaults
      setForegroundRatio([0.85]);
      setVertexCount(-1);
    }
  }, []);

  // Función para manejar favoritos
  const handleToggleFavorite = useCallback(() => {
    if (!current3DModel) return;

    try {
      if (current3DModelSaved) {
        // Quitar de favoritos
        const savedModel = saved3DModels.find(model => model.model_url === current3DModel.model_url);
        if (savedModel) {
          const filteredModels = saved3DModels.filter(model => model.id !== savedModel.id);
          setSaved3DModels(filteredModels);
          setCurrent3DModelSaved(false);

          toast({
            title: "💔 Eliminado de favoritos",
            description: "El modelo 3D ha sido eliminado de tus favoritos.",
          });
        }
      } else {
        // Agregar a favoritos
        const modelData = {
          model_url: current3DModel.model_url,
          filename: current3DModel.filename,
          size_mb: current3DModel.size_mb,
          prompt: current3DModel.prompt,
          metadata: current3DModel.metadata,
          type: modelType,
        };

        const newModel = createSaved3DModel(modelData);
        const updatedModels = [newModel, ...saved3DModels].slice(0, 50); // Limitar a 50

        setSaved3DModels(updatedModels);
        setCurrent3DModelSaved(true);

        toast({
          title: "❤️ ¡Guardado en favoritos!",
          description: "Modelo 3D guardado exitosamente en tus favoritos.",
        });
      }
    } catch (error) {
      console.error('Error al manejar favoritos:', error);

      toast({
        title: "❌ Error",
        description: "No se pudo guardar el modelo 3D. Intenta de nuevo.",
        variant: "destructive",
      });
    }
  }, [current3DModel, current3DModelSaved, saved3DModels, setSaved3DModels, modelType, toast]);

  // Manejar selección de imagen
  const handleImageSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validar imagen
    const validation = validateImageFor3D(file);
    if (!validation.valid) {
      toast({
        title: "❌ Archivo no válido",
        description: validation.error,
        variant: "destructive",
      });
      return;
    }

    setSelectedImage(file);
    
    // Crear preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Limpiar resultado anterior
    setCurrent3DModel(null);
  }, [toast]);

  // Generar modelo 3D
  const handleGenerate3D = useCallback(async () => {
    if (!selectedImage) {
      toast({
        title: "❌ Imagen requerida",
        description: "Por favor, selecciona una imagen primero.",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);
    setProgress(0);
    setProgressMessage("Iniciando generación 3D...");
    setCurrent3DModel(null);

    try {
      const options: Generate3DOptions = {
        image: selectedImage,
        modelType,
        textureResolution,
        foregroundRatio: foregroundRatio[0],
        remesh,
        vertexCount: modelType === 'fast' ? (vertexCount === -1 ? undefined : vertexCount) : undefined,
        // Point Aware 3D específicos
        targetType: modelType === 'point_aware' ? targetType : undefined,
        targetCount: modelType === 'point_aware' ? targetCount : undefined,
        guidanceScale: modelType === 'point_aware' ? guidanceScale[0] : undefined,
        seed: modelType === 'point_aware' && seed > 0 ? seed : undefined,
      };

      const response = await generate3DModel(
        options,
        (progressValue, message) => {
          setProgress(progressValue);
          setProgressMessage(message);
        }
      );

      const new3DModel: Generated3DModel = {
        id: Date.now().toString(),
        model_url: response.model_url,
        filename: response.filename,
        size_mb: response.size_mb,
        prompt: `Imagen: ${selectedImage.name}`,
        metadata: {
          modelType,
          textureResolution,
          foregroundRatio: foregroundRatio[0],
          remesh,
          vertexCount: modelType === 'fast' ? vertexCount : undefined,
          targetType: modelType === 'point_aware' ? targetType : undefined,
          targetCount: modelType === 'point_aware' ? targetCount : undefined,
          guidanceScale: modelType === 'point_aware' ? guidanceScale[0] : undefined,
          seed: modelType === 'point_aware' ? seed : undefined,
        },
        timestamp: Date.now(),
      };

      setCurrent3DModel(new3DModel);

      toast({
        title: "🎉 ¡Modelo 3D generado!",
        description: `Archivo GLB de ${formatFileSize(response.size_mb || 0)} creado exitosamente.`,
      });

    } catch (error) {
      console.error("Error en generación 3D:", error);
      toast({
        title: "❌ Error en generación",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setProgress(0);
      setProgressMessage("");
    }
  }, [selectedImage, modelType, textureResolution, foregroundRatio, remesh, vertexCount, targetType, targetCount, guidanceScale, seed, toast]);

  // Descargar modelo
  const handleDownload = useCallback(() => {
    if (!current3DModel?.model_url || !current3DModel?.filename) return;

    try {
      downloadGLBFile(current3DModel.model_url, current3DModel.filename);
      toast({
        title: "📥 Descarga iniciada",
        description: `Descargando ${current3DModel.filename}`,
      });
    } catch (error) {
      toast({
        title: "❌ Error en descarga",
        description: "No se pudo descargar el archivo",
        variant: "destructive",
      });
    }
  }, [current3DModel, toast]);

  // Verificar si el modelo actual está guardado
  React.useEffect(() => {
    if (current3DModel?.model_url) {
      setCurrent3DModelSaved(is3DModelSaved(current3DModel.model_url, saved3DModels));
    }
  }, [current3DModel, saved3DModels]);

  return (
    <DashboardLayout pageTitle="Generación 3D">
      <TooltipProvider>
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-red-50/30 p-6">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="relative bg-gradient-to-r from-red-600 via-pink-600 to-purple-700 rounded-2xl p-8 mb-8 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-pink-500/20"></div>
              <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

              <div className="relative z-10">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                    <Boxes className="h-8 w-8 text-white" />
                  </div>
                  <h1 className="text-4xl font-bold text-white">
                    Generación 3D
                  </h1>
                </div>
                <p className="text-xl text-red-100 mb-6 max-w-3xl">
                  Convierte cualquier imagen en un modelo 3D detallado usando IA avanzada. Genera modelos GLB listos para usar.
                </p>
                <div className="flex flex-wrap gap-2">
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Boxes className="w-3 h-3 mr-1" />
                    Modelos GLB
                  </Badge>
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Sparkles className="w-3 h-3 mr-1" />
                    IA Avanzada
                  </Badge>
                  <Badge className="bg-white/20 text-white border-white/30">
                    <Settings className="w-3 h-3 mr-1" />
                    Configuración detallada
                  </Badge>
                </div>
              </div>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Panel de Control */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="lg:col-span-1"
            >
              <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Settings className="h-5 w-5 text-red-600" />
                    Panel de Control
                  </CardTitle>
                  <CardDescription>
                    Configura y genera tu modelo 3D perfecto
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Tabs defaultValue="upload" className="w-full">
                    <TabsList className="grid w-full grid-cols-2 mb-6">
                      <TabsTrigger value="upload" className="text-xs">
                        <Upload className="h-3 w-3 mr-1" />
                        Subir Imagen
                      </TabsTrigger>
                      <TabsTrigger value="generate" className="text-xs">
                        <Wand2 className="h-3 w-3 mr-1" />
                        Generar
                      </TabsTrigger>
                    </TabsList>

                    {/* Tab: Subir Imagen */}
                    <TabsContent value="upload" className="space-y-4">
                      <div className="space-y-3">
                        <Label className="text-sm font-medium">Imagen de entrada</Label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept="image/jpeg,image/jpg,image/png,image/webp"
                            onChange={handleImageSelect}
                            className="hidden"
                          />
                          <Button
                            variant="outline"
                            onClick={() => fileInputRef.current?.click()}
                            className="mb-2"
                          >
                            <Upload className="h-4 w-4 mr-2" />
                            Seleccionar Imagen
                          </Button>
                          <p className="text-xs text-gray-500">
                            JPEG, PNG, WebP hasta 10MB
                          </p>
                        </div>

                        {imagePreview && (
                          <div className="relative">
                            <img
                              src={imagePreview}
                              alt="Preview"
                              className="w-full h-48 object-cover rounded-lg border"
                            />
                            <div className="absolute top-2 right-2 bg-black/50 text-white px-2 py-1 rounded text-sm">
                              {selectedImage?.name}
                            </div>
                          </div>
                        )}
                      </div>
                    </TabsContent>

                    {/* Tab: Generar */}
                    <TabsContent value="generate" className="space-y-4">
                      {/* Tipo de modelo */}
                      <div className="space-y-2">
                        <Label>Modelo de generación 3D</Label>
                        <Select value={modelType} onValueChange={(value: any) => handleModelTypeChange(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="fast">Stable Fast 3D (2 créditos, ~30-60s)</SelectItem>
                            <SelectItem value="point_aware">Stable Point Aware 3D (4 créditos, ~2-5min)</SelectItem>
                          </SelectContent>
                        </Select>
                        <div className="text-sm text-muted-foreground">
                          {modelType === 'fast'
                            ? "Generación rápida con buena calidad general"
                            : "Generación avanzada con mayor control y detalle"
                          }
                        </div>
                      </div>

                      {/* Resolución de textura */}
                      <div className="space-y-2">
                        <Label>Resolución de textura</Label>
                        <Select value={textureResolution} onValueChange={(value: any) => setTextureResolution(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="512">512px (Rápido)</SelectItem>
                            <SelectItem value="1024">1024px (Balanceado)</SelectItem>
                            <SelectItem value="2048">2048px (Alta calidad)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Ratio de primer plano */}
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Label>Ratio de primer plano</Label>
                          <Tooltip>
                            <TooltipTrigger>
                              <Info className="w-4 h-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Controla el padding alrededor del objeto</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <Slider
                          value={foregroundRatio}
                          onValueChange={setForegroundRatio}
                          min={modelType === 'point_aware' ? 1.0 : 0.1}
                          max={modelType === 'point_aware' ? 2.0 : 1.0}
                          step={0.05}
                          className="w-full"
                        />
                        <div className="text-sm text-muted-foreground">
                          Valor: {foregroundRatio[0].toFixed(2)}
                          {modelType === 'point_aware' ? ' (1.0-2.0)' : ' (0.1-1.0)'}
                        </div>
                      </div>

                      {/* Tipo de remesh */}
                      <div className="space-y-2">
                        <Label>Algoritmo de remesh</Label>
                        <Select value={remesh} onValueChange={(value: any) => setRemesh(value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">Ninguno</SelectItem>
                            <SelectItem value="triangle">Triángulos</SelectItem>
                            <SelectItem value="quad">Cuadriláteros</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      {/* Parámetros específicos por modelo */}
                      {modelType === 'fast' ? (
                        // Fast 3D específicos
                        <div className="space-y-2">
                          <Label>Límite de vértices (Fast 3D)</Label>
                          <Input
                            type="number"
                            value={vertexCount === -1 ? "" : vertexCount}
                            onChange={(e) => setVertexCount(e.target.value ? parseInt(e.target.value) : -1)}
                            placeholder="Sin límite"
                            min={-1}
                            max={20000}
                          />
                          <div className="text-sm text-muted-foreground">
                            -1 para sin límite, máximo 20,000
                          </div>
                        </div>
                      ) : (
                        // Point Aware 3D específicos (sin mostrar seed)
                        <div className="space-y-4">
                          {/* Target Type */}
                          <div className="space-y-2">
                            <Label>Tipo de simplificación</Label>
                            <Select value={targetType} onValueChange={(value: any) => setTargetType(value)}>
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="none">Ninguna</SelectItem>
                                <SelectItem value="vertex">Por vértices</SelectItem>
                                <SelectItem value="face">Por caras</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          {/* Target Count */}
                          {targetType !== 'none' && (
                            <div className="space-y-2">
                              <Label>Cantidad objetivo</Label>
                              <Input
                                type="number"
                                value={targetCount}
                                onChange={(e) => setTargetCount(parseInt(e.target.value) || 1000)}
                                min={100}
                                max={20000}
                              />
                              <div className="text-sm text-muted-foreground">
                                Número de {targetType === 'vertex' ? 'vértices' : 'caras'} objetivo (100-20,000)
                              </div>
                            </div>
                          )}

                          {/* Guidance Scale */}
                          <div className="space-y-2">
                            <div className="flex items-center gap-2">
                              <Label>Escala de guía</Label>
                              <Tooltip>
                                <TooltipTrigger>
                                  <Info className="w-4 h-4 text-muted-foreground" />
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Controla el nivel de detalle. Valores bajos = menos detalle, valores altos = posibles artefactos</p>
                                </TooltipContent>
                              </Tooltip>
                            </div>
                            <Slider
                              value={guidanceScale}
                              onValueChange={setGuidanceScale}
                              min={1.0}
                              max={10.0}
                              step={0.1}
                              className="w-full"
                            />
                            <div className="text-sm text-muted-foreground">
                              Valor: {guidanceScale[0].toFixed(1)} (1.0-10.0)
                            </div>
                          </div>
                        </div>
                      )}

                      <Button
                        onClick={handleGenerate3D}
                        disabled={!selectedImage || isGenerating}
                        className="w-full bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700"
                      >
                        {isGenerating ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Generando...
                          </>
                        ) : (
                          <>
                            <Wand2 className="h-4 w-4 mr-2" />
                            Generar Modelo 3D
                          </>
                        )}
                      </Button>

                      {/* Progreso */}
                      {isGenerating && (
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Progreso</span>
                            <span>{progress}%</span>
                          </div>
                          <Progress value={progress} className="w-full" />
                          <p className="text-sm text-muted-foreground">{progressMessage}</p>
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </CardContent>
              </Card>
            </motion.div>

            {/* Área de Visualización */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
              className="lg:col-span-2"
            >
              <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="latest" className="flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Última Generación
                  </TabsTrigger>
                  <TabsTrigger value="saved" className="flex items-center gap-2">
                    <Heart className="h-4 w-4" />
                    Guardados ({saved3DModels.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="latest" className="space-y-6">
                  {/* Modelo 3D generado */}
                  {current3DModel && (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <CardTitle className="flex items-center gap-2">
                            <Boxes className="h-5 w-5 text-red-600" />
                            Modelo 3D Generado
                          </CardTitle>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={handleToggleFavorite}
                              className={current3DModelSaved ? "text-red-500 border-red-200 bg-red-50" : ""}
                            >
                              <Heart className={`h-4 w-4 mr-1 ${current3DModelSaved ? "fill-current" : ""}`} />
                              {current3DModelSaved ? "Guardado" : "Guardar"}
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={handleDownload}
                            >
                              <Download className="h-4 w-4 mr-1" />
                              Descargar GLB
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={async () => {
                                try {
                                  await navigator.clipboard.writeText(current3DModel.model_url);
                                  toast({
                                    title: "Enlace copiado",
                                    description: "El enlace del modelo 3D se copió al portapapeles",
                                  });
                                } catch (error) {
                                  toast({
                                    title: "Error",
                                    description: "No se pudo copiar el enlace",
                                    variant: "destructive",
                                  });
                                }
                              }}
                            >
                              <Share2 className="h-4 w-4 mr-1" />
                              Compartir
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                    </Card>
                  )}

                  {/* Estado vacío */}
                  {!current3DModel && (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center h-64 text-center">
                        <Boxes className="h-16 w-16 text-gray-400 mb-4" />
                        <h3 className="text-lg font-semibold text-gray-600 mb-2">
                          No hay modelo 3D generado
                        </h3>
                        <p className="text-gray-500 mb-4">
                          Sube una imagen y configura los parámetros para generar tu primer modelo 3D
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>

                <TabsContent value="saved" className="space-y-6">
                  {saved3DModels.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {saved3DModels.map((model) => (
                        <Card key={model.id} className="shadow-lg border-0 bg-white/80 backdrop-blur-sm">
                          <CardHeader className="pb-2">
                            <div className="flex items-center justify-between">
                              <CardTitle className="text-sm font-medium truncate">
                                {model.filename}
                              </CardTitle>
                              <Badge variant="outline" className="text-xs">
                                {model.type === 'fast' ? 'Fast 3D' : 'Point Aware 3D'}
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="space-y-3">
                            <div className="text-xs text-gray-500 space-y-1">
                              <p><strong>Tamaño:</strong> {formatFileSize(model.size_mb || 0)}</p>
                              <p><strong>Fecha:</strong> {new Date(model.timestamp).toLocaleDateString()}</p>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => downloadGLBFile(model.model_url, model.filename)}
                                className="flex-1"
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={async () => {
                                  try {
                                    await navigator.clipboard.writeText(model.model_url);
                                    toast({
                                      title: "Enlace copiado",
                                      description: "El enlace del modelo 3D se copió al portapapeles",
                                    });
                                  } catch (error) {
                                    toast({
                                      title: "Error",
                                      description: "No se pudo copiar el enlace",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                className="flex-1"
                              >
                                <Share2 className="h-3 w-3 mr-1" />
                                Compartir
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const filteredModels = saved3DModels.filter(m => m.id !== model.id);
                                  setSaved3DModels(filteredModels);
                                  toast({
                                    title: "Modelo eliminado",
                                    description: "El modelo 3D ha sido eliminado de tus favoritos.",
                                  });
                                }}
                                className="text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                      <CardContent className="flex flex-col items-center justify-center h-64 text-center">
                        <Heart className="h-16 w-16 text-gray-400 mb-4" />
                        <h3 className="text-lg font-semibold text-gray-600 mb-2">
                          No tienes modelos 3D guardados
                        </h3>
                        <p className="text-gray-500 mb-4">
                          Los modelos 3D que marques como favoritos aparecerán aquí
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
              </Tabs>
            </motion.div>
          </div>
        </div>
      </TooltipProvider>
    </DashboardLayout>
  );
}
