/**
 * Image Generator Page - Generación de imágenes con IA
 * Basado en logo-generator-page.tsx con el mismo layout y funcionalidad
 */

import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Sparkles,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  HeartOff,
  Upload,
  X,
  Brush,
  ImageIcon,
  Edit3,
  Trash2,
  Eye,
  EyeOff,
  RotateCcw,
  Settings,
  Zap,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  generateLogoText,
  editWithReferences,
  editWithMask,
  validateImageFile,
  type LogoTextGenerationOptions,
  type LogoTextResponse
} from "@/services/logo-text-service";

// Tipos para el estado de la aplicación
interface GeneratedImage {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  timestamp: number;
}

interface SavedImage {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  type: "basic" | "reference" | "mask_edit";
  timestamp: number;
}

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Utilidades para imágenes guardadas
const SAVED_IMAGES_KEY = "emma-saved-images";

function createSavedImage(data: {
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  type: "basic" | "reference" | "mask_edit";
}): SavedImage {
  return {
    id: `image-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: Date.now(),
    ...data,
  };
}

function isImageSaved(imageUrl: string, savedImages: SavedImage[]): boolean {
  return savedImages.some(image => image.image_url === imageUrl);
}

export default function ImageGeneratorPage() {
  // Estados principales
  const [currentImage, setCurrentImage] = useState<GeneratedImage | null>(null);
  const [prompt, setPrompt] = useState("");
  const [size, setSize] = useState<"1024x1024" | "1536x1024" | "1024x1536">("1024x1024");
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para edición con referencias
  const [referenceImages, setReferenceImages] = useState<File[]>([]);
  const [referencePrompt, setReferencePrompt] = useState("");

  // Estados para edición con máscara
  const [editingImage, setEditingImage] = useState<string | null>(null);
  const [maskPrompt, setMaskPrompt] = useState("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [showMask, setShowMask] = useState(true);

  // Estados para favoritos
  const [savedImages, setSavedImages] = useLocalStorage<SavedImage[]>(SAVED_IMAGES_KEY, []);
  const [currentImageSaved, setCurrentImageSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo para imágenes
  const examplePrompts = [
    "Un paisaje futurista con ciudades flotantes y luces neón",
    "Retrato artístico de una persona en estilo cyberpunk con elementos digitales",
    "Naturaleza abstracta con colores vibrantes y formas geométricas",
    "Arquitectura moderna con elementos orgánicos y diseño sostenible",
    "Escena de fantasía con criaturas mágicas en un bosque encantado",
    "Arte digital minimalista con paleta de colores suaves"
  ];

  // Tamaños disponibles según documentación OpenAI
  const sizeOptions = [
    { value: "1024x1024", label: "Cuadrado (1024x1024)", icon: "⬜" },
    { value: "1536x1024", label: "Horizontal (1536x1024)", icon: "▭" },
    { value: "1024x1536", label: "Vertical (1024x1536)", icon: "▯" }
  ];

  // Función para generar imagen (usando OpenAI)
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt requerido",
        description: "Por favor, describe la imagen que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: LogoTextGenerationOptions = {
        prompt,
        size,
      };

      const result = await generateLogoText(options);

      if (result.success && result.image_url) {
        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt,
          revised_prompt: result.revised_prompt,
          response_id: result.response_id,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentImage(newImage);

        toast({
          title: "¡Imagen generada!",
          description: "Tu imagen ha sido creada exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating image:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para cargar imagen para editar
  const handleLoadImageForEdit = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setEditingImage(imageUrl);

      // Cargar imagen en el canvas
      setTimeout(() => {
        const canvas = canvasRef.current;
        const maskCanvas = maskCanvasRef.current;
        if (canvas && maskCanvas) {
          const ctx = canvas.getContext('2d');
          const maskCtx = maskCanvas.getContext('2d');
          const img = new Image();

          img.onload = () => {
            // Calcular tamaño apropiado para el canvas (máximo 600px de ancho)
            const maxWidth = 600;
            const maxHeight = 400;
            let { width, height } = img;

            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }

            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }

            // Configurar tamaño del canvas
            canvas.width = width;
            canvas.height = height;
            maskCanvas.width = width;
            maskCanvas.height = height;

            // Aplicar tamaño CSS para que se vea correctamente
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            maskCanvas.style.width = `${width}px`;
            maskCanvas.style.height = `${height}px`;

            // Dibujar imagen original escalada
            ctx?.drawImage(img, 0, 0, width, height);

            // Limpiar máscara (fondo negro)
            if (maskCtx) {
              maskCtx.fillStyle = 'black';
              maskCtx.fillRect(0, 0, width, height);
            }
          };

          img.src = imageUrl;
        }
      }, 100);
    };
    reader.readAsDataURL(file);
  };

  // Funciones para dibujar en el canvas
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();

    // Calcular la escala entre el canvas real y el canvas mostrado
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    // Ajustar coordenadas según la escala
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.arc(x, y, (brushSize / 2) * scaleX, 0, 2 * Math.PI);
    ctx.fill();
  };

  // Función para limpiar máscara
  const clearMask = () => {
    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  // Función para manejar archivos de referencia
  const handleReferenceFiles = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      const validation = validateImageFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    if (errors.length > 0) {
      toast({
        title: "Archivos inválidos",
        description: errors.join(", "),
        variant: "destructive",
      });
    }

    if (validFiles.length > 0) {
      setReferenceImages(prev => [...prev, ...validFiles].slice(0, 4)); // Máximo 4 imágenes
    }
  };

  // Función para generar con referencias
  const handleGenerateWithReferences = async () => {
    if (!referencePrompt.trim() || referenceImages.length === 0) {
      toast({
        title: "Datos requeridos",
        description: "Necesitas un prompt y al menos una imagen de referencia",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const result = await editWithReferences({
        prompt: referencePrompt,
        referenceImages,
        size,
      });

      if (result.success && result.image_url) {
        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: referencePrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentImage(newImage);

        toast({
          title: "¡Imagen generada!",
          description: "Tu imagen con referencias ha sido creada exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating with references:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar con referencias",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para editar con máscara integrada
  const handleEditWithMask = async () => {
    if (!maskPrompt.trim() || !editingImage) {
      toast({
        title: "Datos requeridos",
        description: "Necesitas cargar una imagen y escribir qué quieres cambiar",
        variant: "destructive",
      });
      return;
    }

    const canvas = canvasRef.current;
    const maskCanvas = maskCanvasRef.current;

    if (!canvas || !maskCanvas) {
      toast({
        title: "Error",
        description: "Canvas no disponible",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Convertir canvas a archivos
      const imageBlob = await new Promise<Blob>((resolve) => {
        canvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      const maskBlob = await new Promise<Blob>((resolve) => {
        maskCanvas.toBlob((blob) => resolve(blob!), 'image/png');
      });

      // Crear archivos File
      const imageFile = new File([imageBlob], 'image.png', { type: 'image/png' });
      const maskFile = new File([maskBlob], 'mask.png', { type: 'image/png' });

      const result = await editWithMask({
        prompt: maskPrompt,
        image: imageFile,
        mask: maskFile,
      });

      if (result.success && result.image_url) {
        const newImage: GeneratedImage = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt: maskPrompt,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentImage(newImage);
        setMaskPrompt("");

        toast({
          title: "¡Imagen editada!",
          description: "Los cambios han sido aplicados exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error editing with mask:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al editar la imagen",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Funciones para compatibilidad con el código existente (no se usan en el nuevo layout)
  const handleImageTextGenerate = async () => {
    toast({
      title: "Funcionalidad no disponible",
      description: "Esta función no está disponible en el nuevo layout",
      variant: "destructive",
    });
  };

  const handleImageTextReferenceGenerate = async () => {
    toast({
      title: "Funcionalidad no disponible",
      description: "Esta función no está disponible en el nuevo layout",
      variant: "destructive",
    });
  };

  // Estados para compatibilidad
  const [imageTextPrompt, setImageTextPrompt] = useState("");
  const [imageTextReferenceImages, setImageTextReferenceImages] = useState<File[]>([]);
  const [imageTextReferencePrompt, setImageTextReferencePrompt] = useState("");
  const [isImageTextGenerating, setIsImageTextGenerating] = useState(false);
  const [currentImageTextImage, setCurrentImageTextImage] = useState<any>(null);

  // Función para verificar si la imagen actual está guardada
  React.useEffect(() => {
    if (currentImage) {
      const isImageSaved = savedImages.some(img => img.image_url === currentImage.image_url);
      setCurrentImageSaved(isImageSaved);
    }
  }, [currentImage, savedImages]);

  return (
    <DashboardLayout pageTitle="Generador de Imágenes IA">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Generador de Imágenes IA
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Crea imágenes impactantes con IA. Genera desde cero, usa referencias visuales o edita con precisión.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Brush className="w-3 h-3 mr-1" />
                  Editor integrado
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="w-3 h-3 mr-1" />
                  Referencias visuales
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Edición precisa
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Configura y genera tu imagen perfecta
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="generate" className="w-full">
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="generate" className="text-xs">
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generar
                    </TabsTrigger>
                    <TabsTrigger value="references" className="text-xs">
                      <ImageIcon className="h-3 w-3 mr-1" />
                      Referencias
                    </TabsTrigger>
                    <TabsTrigger value="edit" className="text-xs">
                      <Edit3 className="h-3 w-3 mr-1" />
                      Editar
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Generar */}
                  <TabsContent value="generate" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imagen a Generar</Label>
                      <Textarea
                        placeholder="Describe la imagen que quieres crear..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">
                        Tamaño de la Imagen
                        <span className="ml-2 text-xs text-gray-500">
                          (Actual: {sizeOptions.find(opt => opt.value === size)?.label})
                        </span>
                      </Label>
                      <div className="grid grid-cols-1 gap-2">
                        {sizeOptions.map((option) => (
                          <Button
                            key={option.value}
                            variant={size === option.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSize(option.value as "1024x1024" | "1536x1024" | "1024x1536")}
                            className={`justify-start transition-all ${
                              size === option.value
                                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                                : "hover:bg-purple-50 hover:border-purple-300"
                            }`}
                          >
                            <span className="mr-2 text-lg">{option.icon}</span>
                            <div className="text-left">
                              <div className="font-medium">{option.label.split(' (')[0]}</div>
                              <div className="text-xs opacity-75">
                                {option.label.split('(')[1]?.replace(')', '')}
                              </div>
                            </div>
                            {size === option.value && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                              </div>
                            )}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Generar Imagen
                        </>
                      )}
                    </Button>

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  {/* Tab: Referencias */}
                  <TabsContent value="references" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la Imagen</Label>
                      <Textarea
                        placeholder="Describe la imagen que quieres crear basada en las referencias..."
                        value={referencePrompt}
                        onChange={(e) => setReferencePrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imágenes de Referencia (máx. 4)</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={fileInputRef}
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => handleReferenceFiles(e.target.files)}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Subir Imágenes
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB cada una
                        </p>
                      </div>

                      {/* Mostrar imágenes de referencia */}
                      {referenceImages.length > 0 && (
                        <div className="grid grid-cols-2 gap-2">
                          {referenceImages.map((file, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Referencia ${index + 1}`}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                              <Button
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => {
                                  setReferenceImages(prev => prev.filter((_, i) => i !== index));
                                }}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    {referenceImages.length > 0 && (
                      <Button
                        onClick={handleGenerateWithReferences}
                        disabled={isGenerating || !referencePrompt.trim()}
                        className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                      >
                        {isGenerating ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Generando...
                          </>
                        ) : (
                          <>
                            <ImageIcon className="h-4 w-4 mr-2" />
                            Generar con Referencias
                          </>
                        )}
                      </Button>
                    )}
                  </TabsContent>

                  {/* Tab: Editar */}
                  <TabsContent value="edit" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Cargar Imagen para Editar</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={imageInputRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const validation = validateImageFile(file);
                              if (validation.valid) {
                                handleLoadImageForEdit(file);
                              } else {
                                toast({
                                  title: "Archivo inválido",
                                  description: validation.error,
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => imageInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Cargar Imagen
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB
                        </p>
                      </div>
                    </div>

                    {editingImage && (
                      <>
                        <div className="space-y-3">
                          <Label className="text-sm font-medium">Qué quieres cambiar</Label>
                          <Textarea
                            placeholder="Describe qué quieres cambiar en las áreas marcadas..."
                            value={maskPrompt}
                            onChange={(e) => setMaskPrompt(e.target.value)}
                            className="min-h-[80px] resize-none"
                          />
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Herramientas de Edición</Label>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowMask(!showMask)}
                              >
                                {showMask ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={clearMask}
                              >
                                <RotateCcw className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-xs text-gray-600">Tamaño del pincel: {brushSize}px</Label>
                            <Slider
                              value={[brushSize]}
                              onValueChange={(value) => setBrushSize(value[0])}
                              max={50}
                              min={5}
                              step={5}
                              className="w-full"
                            />
                          </div>
                        </div>

                        <Button
                          onClick={handleEditWithMask}
                          disabled={isGenerating || !maskPrompt.trim()}
                          className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700"
                        >
                          {isGenerating ? (
                            <>
                              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                              Editando...
                            </>
                          ) : (
                            <>
                              <Edit3 className="h-4 w-4 mr-2" />
                              Aplicar Cambios
                            </>
                          )}
                        </Button>
                      </>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>

          {/* Área de Visualización */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="latest" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Última Generación
                </TabsTrigger>
                <TabsTrigger value="saved" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Guardados ({savedImages.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-6">
                {/* Mostrar imagen generada */}
                {currentImage ? (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                    <div className="relative">
                      <img
                        src={currentImage.image_url}
                        alt="Imagen generada"
                        className="w-full h-auto max-h-[600px] object-contain"
                      />
                      <div className="absolute top-4 right-4 flex gap-2">
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => {
                            const link = document.createElement('a');
                            link.href = currentImage.image_url;
                            link.download = `imagen-${Date.now()}.png`;
                            link.click();
                          }}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Descargar
                        </Button>
                        <Button
                          size="sm"
                          variant={currentImageSaved ? "default" : "secondary"}
                          onClick={() => {
                            if (currentImageSaved) {
                              setSavedImages(prev => prev.filter(img => img.image_url !== currentImage.image_url));
                              setCurrentImageSaved(false);
                              toast({
                                title: "Imagen eliminada de favoritos",
                                description: "La imagen ha sido removida de tus guardados",
                              });
                            } else {
                              const savedImage = createSavedImage({
                                image_url: currentImage.image_url,
                                prompt: currentImage.prompt,
                                revised_prompt: currentImage.revised_prompt,
                                metadata: currentImage.metadata,
                                type: "basic",
                              });
                              setSavedImages(prev => [savedImage, ...prev]);
                              setCurrentImageSaved(true);
                              toast({
                                title: "Imagen guardada",
                                description: "La imagen ha sido añadida a tus favoritos",
                              });
                            }
                          }}
                        >
                          {currentImageSaved ? <Heart className="h-4 w-4 mr-1 fill-current" /> : <HeartOff className="h-4 w-4 mr-1" />}
                          {currentImageSaved ? "Guardado" : "Guardar"}
                        </Button>
                      </div>
                    </div>

                    <CardContent className="p-6">
                      <div className="space-y-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Prompt Original</Label>
                          <p className="text-sm text-gray-600 mt-1">{currentImage.prompt}</p>
                        </div>
                        {currentImage.revised_prompt && (
                          <div>
                            <Label className="text-sm font-medium text-gray-700">Prompt Revisado</Label>
                            <p className="text-sm text-gray-600 mt-1">{currentImage.revised_prompt}</p>
                          </div>
                        )}
                        <div className="text-xs text-gray-400">
                          Generado el {new Date(currentImage.timestamp).toLocaleDateString('es-ES', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ) : editingImage ? (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Edit3 className="h-5 w-5 text-green-600" />
                        Editor de Imagen
                      </CardTitle>
                      <CardDescription>
                        Pinta sobre las áreas que quieres cambiar
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="relative inline-block">
                        <canvas
                          ref={canvasRef}
                          className="border rounded-lg max-w-full h-auto"
                        />
                        <canvas
                          ref={maskCanvasRef}
                          className={`absolute top-0 left-0 border rounded-lg max-w-full h-auto cursor-crosshair ${
                            showMask ? 'opacity-50' : 'opacity-0'
                          }`}
                          style={{ mixBlendMode: 'multiply' }}
                          onMouseDown={startDrawing}
                          onMouseMove={draw}
                          onMouseUp={stopDrawing}
                          onMouseLeave={stopDrawing}
                        />
                      </div>
                    </CardContent>
                  </Card>
                ) : (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                        <Sparkles className="h-12 w-12 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        ¡Crea tu primera imagen!
                      </h3>
                      <p className="text-gray-600 text-center max-w-md mb-6">
                        Describe la imagen que quieres crear, usa referencias visuales o edita una imagen existente.
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        <Badge variant="secondary">Artístico</Badge>
                        <Badge variant="secondary">Fotográfico</Badge>
                        <Badge variant="secondary">Abstracto</Badge>
                        <Badge variant="secondary">Futurista</Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="saved" className="space-y-6">
                {savedImages.length === 0 ? (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                        <Heart className="h-12 w-12 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        No tienes imágenes guardadas
                      </h3>
                      <p className="text-gray-600 text-center max-w-md">
                        Las imágenes que marques como favoritas aparecerán aquí para acceso rápido.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {savedImages.map((savedImage) => (
                      <Card key={savedImage.id} className="shadow-xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                        <div className="relative group">
                          <img
                            src={savedImage.image_url}
                            alt="Imagen guardada"
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
                        </div>

                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-start gap-2">
                              <Badge variant="secondary" className="mt-0.5 text-xs">
                                {savedImage.type === "basic" ? "Generada" :
                                 savedImage.type === "reference" ? "Referencias" : "Editada"}
                              </Badge>
                              <p className="text-sm text-gray-600 flex-1 line-clamp-2">
                                {savedImage.prompt}
                              </p>
                            </div>

                            <div className="text-xs text-gray-400">
                              {new Date(savedImage.timestamp).toLocaleDateString('es-ES', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1"
                                onClick={() => {
                                  setCurrentImage({
                                    id: savedImage.id,
                                    image_url: savedImage.image_url,
                                    prompt: savedImage.prompt,
                                    revised_prompt: savedImage.revised_prompt,
                                    metadata: savedImage.metadata,
                                    timestamp: savedImage.timestamp,
                                  });
                                  setMainTab("latest");
                                }}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const link = document.createElement('a');
                                  link.href = savedImage.image_url;
                                  link.download = `imagen-guardada-${Date.now()}.png`;
                                  link.click();
                                }}
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                                onClick={() => {
                                  setSavedImages(prev => prev.filter(img => img.id !== savedImage.id));
                                  toast({
                                    title: "Imagen eliminada",
                                    description: "La imagen ha sido removida de tus guardados",
                                  });
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
}
