/**
 * Image Generator Page - Generación de imágenes con IA
 * Basado en poster-creator-page.tsx con el mismo estilo y layout
 */

import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Sparkles,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  HeartOff,
  Upload,
  X,
  Brush,
  ImageIcon,
  Edit3,
  Trash2,
  Eye,
  Settings,
  Zap,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";

// Tipos para el estado de la aplicación
interface GeneratedImage {
  id: string;
  image_url: string;
  prompt: string;
  model: string;
  stylePreset: string;
  timestamp: number;
}

interface SavedImage {
  id: string;
  image_url: string;
  prompt: string;
  model: string;
  stylePreset: string;
  timestamp: number;
}

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Utilidades para imágenes guardadas
const SAVED_IMAGES_KEY = "emma-saved-images";

// Modelos de IA disponibles
const AI_MODELS = [
  {
    value: "ultra",
    label: "Calidad",
    description: "Máxima calidad",
    icon: Sparkles,
    credits: 8,
  },
  {
    value: "core",
    label: "Rápido", 
    description: "Generación rápida",
    icon: Zap,
    credits: 4,
  },
  {
    value: "sd3",
    label: "Balanceado",
    description: "Equilibrio perfecto",
    icon: Settings,
    credits: 6,
  },
];

// Estilos visuales
const STYLE_PRESETS = [
  { value: "photographic", label: "Fotográfico", description: "Realista y detallado" },
  { value: "digital-art", label: "Arte Digital", description: "Estilo artístico moderno" },
  { value: "comic-book", label: "Cómic", description: "Estilo de cómic" },
  { value: "fantasy-art", label: "Fantasía", description: "Arte fantástico" },
  { value: "line-art", label: "Arte Lineal", description: "Dibujos con líneas" },
  { value: "analog-film", label: "Película Analógica", description: "Estilo vintage" },
  { value: "neon-punk", label: "Neon Punk", description: "Cyberpunk neón" },
  { value: "isometric", label: "Isométrico", description: "Vista isométrica" },
  { value: "low-poly", label: "Low Poly", description: "Estilo poligonal" },
  { value: "origami", label: "Origami", description: "Estilo papel plegado" },
];

export default function ImageGeneratorPage() {
  // Estados principales
  const [currentImage, setCurrentImage] = useState<GeneratedImage | null>(null);
  const [prompt, setPrompt] = useState("");
  const [selectedModel, setSelectedModel] = useState("ultra");
  const [stylePreset, setStylePreset] = useState("photographic");
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para imagen con texto
  const [imageTextPrompt, setImageTextPrompt] = useState("");
  const [imageTextReferenceImages, setImageTextReferenceImages] = useState<File[]>([]);
  const [imageTextReferencePrompt, setImageTextReferencePrompt] = useState("");
  const [isImageTextGenerating, setIsImageTextGenerating] = useState(false);

  // Estados para favoritos
  const [savedImages, setSavedImages] = useLocalStorage<SavedImage[]>(SAVED_IMAGES_KEY, []);
  const [currentImageSaved, setCurrentImageSaved] = useState(false);
  const [currentImageTextSaved, setCurrentImageTextSaved] = useState(false);
  const [currentImageTextImage, setCurrentImageTextImage] = useState<any>(null);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  // Hooks
  const { toast } = useToast();

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo
  const examplePrompts = [
    "Un paisaje futurista con ciudades flotantes",
    "Retrato artístico de una persona en estilo cyberpunk", 
    "Naturaleza abstracta con colores vibrantes",
    "Arquitectura moderna con elementos orgánicos",
    "Escena de fantasía con criaturas mágicas",
  ];

  // Función para generar imagen (Generación Standard)
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor, describe la imagen que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      // Simular generación por ahora
      setTimeout(() => {
        setIsGenerating(false);
        toast({
          title: "🎨 Generación iniciada",
          description: "La funcionalidad estará disponible pronto",
        });
      }, 2000);

    } catch (error) {
      console.error("Error al generar imagen:", error);
      toast({
        title: "Error al generar imagen",
        description: error instanceof Error ? error.message : "Ha ocurrido un error inesperado",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para generar imagen con texto
  const handleImageTextGenerate = async () => {
    if (!imageTextPrompt.trim()) {
      toast({
        title: "Descripción requerida",
        description: "Por favor, describe la imagen que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsImageTextGenerating(true);
    // Aquí iría la lógica para generar con OpenAI
    setTimeout(() => {
      setIsImageTextGenerating(false);
      toast({
        title: "Funcionalidad próximamente",
        description: "La generación con texto estará disponible pronto",
      });
    }, 2000);
  };

  // Función para generar con referencias
  const handleImageTextReferenceGenerate = async () => {
    if (!imageTextReferencePrompt.trim() || imageTextReferenceImages.length === 0) {
      toast({
        title: "Datos incompletos",
        description: "Necesitas una descripción y al menos una imagen de referencia",
        variant: "destructive",
      });
      return;
    }

    setIsImageTextGenerating(true);
    // Aquí iría la lógica para generar con referencias
    setTimeout(() => {
      setIsImageTextGenerating(false);
      toast({
        title: "Funcionalidad próximamente",
        description: "La generación con referencias estará disponible pronto",
      });
    }, 2000);
  };

  return (
    <DashboardLayout pageTitle="Generador de Imágenes IA">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Sparkles className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Generador de Imágenes IA
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Crea imágenes impactantes con IA. Genera desde cero, usa referencias visuales o edita con precisión.
              </p>
              <div className="flex flex-wrap gap-2">
                <Badge className="bg-white/20 text-white border-white/30">
                  <Brush className="w-3 h-3 mr-1" />
                  Editor integrado
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <ImageIcon className="w-3 h-3 mr-1" />
                  Referencias visuales
                </Badge>
                <Badge className="bg-white/20 text-white border-white/30">
                  <Edit3 className="w-3 h-3 mr-1" />
                  Edición precisa
                </Badge>
              </div>
            </div>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Configura y genera tu imagen perfecta
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="generate" className="w-full">
                  <TabsList className="grid w-full grid-cols-2 mb-6">
                    <TabsTrigger value="generate" className="text-xs">
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generar
                    </TabsTrigger>
                    <TabsTrigger value="image-text" className="text-xs">
                      <ImageIcon className="h-3 w-3 mr-1" />
                      Con Texto
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Generar (Generación Standard) */}
                  <TabsContent value="generate" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la Imagen</Label>
                      <Textarea
                        placeholder="Describe la imagen que quieres crear..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">
                        Modelo de IA
                        <span className="ml-2 text-xs text-gray-500">
                          (Actual: {AI_MODELS.find(m => m.value === selectedModel)?.label})
                        </span>
                      </Label>
                      <div className="grid grid-cols-1 gap-2">
                        {AI_MODELS.map((model) => (
                          <Button
                            key={model.value}
                            variant={selectedModel === model.value ? "default" : "outline"}
                            size="sm"
                            onClick={() => setSelectedModel(model.value)}
                            className={`justify-start transition-all ${
                              selectedModel === model.value
                                ? "bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                                : "hover:bg-purple-50 hover:border-purple-300"
                            }`}
                          >
                            <model.icon className="mr-2 h-4 w-4" />
                            <div className="text-left">
                              <div className="font-medium">{model.label}</div>
                              <div className="text-xs opacity-75">
                                {model.description}
                              </div>
                            </div>
                            {selectedModel === model.value && (
                              <div className="ml-auto">
                                <div className="w-2 h-2 bg-white rounded-full"></div>
                              </div>
                            )}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Estilo Visual</Label>
                      <Select value={stylePreset} onValueChange={setStylePreset}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona un estilo" />
                        </SelectTrigger>
                        <SelectContent className="max-h-60">
                          {STYLE_PRESETS.map((style) => (
                            <SelectItem key={style.value} value={style.value}>
                              <div className="flex flex-col">
                                <span className="font-medium">{style.label}</span>
                                <span className="text-xs text-gray-500">{style.description}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Generar Imagen
                        </>
                      )}
                    </Button>

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  {/* Tab: Con Texto (incluye Referencias y Editar) */}
                  <TabsContent value="image-text" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción de la Imagen</Label>
                      <Textarea
                        placeholder="Describe cualquier imagen que puedas imaginar..."
                        value={imageTextPrompt}
                        onChange={(e) => setImageTextPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    <Button
                      onClick={handleImageTextGenerate}
                      disabled={isImageTextGenerating || !imageTextPrompt.trim()}
                      className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                    >
                      {isImageTextGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Generar Imagen
                        </>
                      )}
                    </Button>

                    {/* Sección de Referencias */}
                    <div className="space-y-3 pt-4 border-t">
                      <Label className="text-sm font-medium">Referencias Visuales (Opcional)</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => {
                            const files = Array.from(e.target.files || []);
                            setImageTextReferenceImages(prev => [...prev, ...files].slice(0, 4));
                            e.target.value = '';
                          }}
                          className="hidden"
                          id="imageTextReferenceInput"
                        />
                        <Button
                          variant="outline"
                          onClick={() => document.getElementById('imageTextReferenceInput')?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Subir Referencias
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB cada una (máx. 4)
                        </p>
                      </div>

                      {imageTextReferenceImages.length > 0 && (
                        <div className="grid grid-cols-2 gap-2">
                          {imageTextReferenceImages.map((file, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Referencia ${index + 1}`}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                              <Button
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => {
                                  setImageTextReferenceImages(prev => prev.filter((_, i) => i !== index));
                                }}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}

                      {imageTextReferenceImages.length > 0 && (
                        <>
                          <div className="space-y-2">
                            <Label className="text-sm font-medium">Descripción con Referencias</Label>
                            <Textarea
                              placeholder="Describe la imagen basada en las referencias..."
                              value={imageTextReferencePrompt}
                              onChange={(e) => setImageTextReferencePrompt(e.target.value)}
                              className="min-h-[80px] resize-none"
                            />
                          </div>
                          <Button
                            onClick={handleImageTextReferenceGenerate}
                            disabled={isImageTextGenerating || !imageTextReferencePrompt.trim()}
                            className="w-full bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                          >
                            <ImageIcon className="h-4 w-4 mr-2" />
                            Generar con Referencias
                          </Button>
                        </>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>

          {/* Área de Visualización */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="latest" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Última Generación
                </TabsTrigger>
                <TabsTrigger value="saved" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Guardados ({savedImages.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-6">
                {/* Estado vacío */}
                {!currentImage && !currentImageTextImage && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                        <Sparkles className="h-12 w-12 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        ¡Crea tu primera imagen!
                      </h3>
                      <p className="text-gray-600 text-center max-w-md mb-6">
                        Describe la imagen que quieres crear, usa referencias visuales o genera con libertad creativa total.
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        <Badge variant="secondary">Artístico</Badge>
                        <Badge variant="secondary">Fotográfico</Badge>
                        <Badge variant="secondary">Abstracto</Badge>
                        <Badge variant="secondary">Futurista</Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="saved" className="space-y-6">
                {savedImages.length === 0 ? (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                        <Heart className="h-12 w-12 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        No tienes imágenes guardadas
                      </h3>
                      <p className="text-gray-600 text-center max-w-md">
                        Las imágenes que marques como favoritas aparecerán aquí para acceso rápido.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {savedImages.map((savedImage) => {
                      const modelInfo = AI_MODELS.find(m => m.value === savedImage.model);
                      const styleInfo = STYLE_PRESETS.find(s => s.value === savedImage.stylePreset);

                      return (
                        <Card key={`standard-${savedImage.id}`} className="shadow-xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                          <div className="relative group">
                            <img
                              src={savedImage.image_url}
                              alt="Imagen guardada"
                              className="w-full h-48 object-cover"
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
                          </div>

                          <CardContent className="p-4">
                            <div className="space-y-3">
                              <div className="flex items-start gap-2">
                                <Badge variant="secondary" className="mt-0.5 text-xs">
                                  Standard
                                </Badge>
                                <p className="text-sm text-gray-600 flex-1 line-clamp-2">
                                  {savedImage.prompt}
                                </p>
                              </div>

                              <div className="text-xs text-gray-400">
                                {new Date(savedImage.timestamp).toLocaleDateString('es-ES', {
                                  year: 'numeric',
                                  month: 'short',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </div>

                              {/* Botones de acción */}
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  className="flex-1"
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  Ver
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                >
                                  <Download className="h-3 w-3 mr-1" />
                                  Descargar
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </DashboardLayout>
  );
}
