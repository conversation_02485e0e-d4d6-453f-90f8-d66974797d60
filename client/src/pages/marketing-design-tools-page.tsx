import { useState } from "react";
import { motion } from "framer-motion";
import { useLocation } from "wouter";
import {
  AITool,
  aiToolsData,
  IMPLEMENTED_TOOLS,
  getToolRoute,
  isToolImplemented,
} from "@/data/ai-tools-data";
import DashboardLayoutWrapper from "@/components/layout/dashboard-layout";
import {
  ChevronRight,
  ArrowRight,
  ArrowLeft,
  Sparkles,
  Heart,
  Star,
  Home,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useFavorites } from "@/hooks/use-favorites";
import { Breadcrumbs } from "@/components/ui/breadcrumbs";

// Emma brand colors
const EMMA_COLORS = {
  primary: "#3018ef", // Emma blue
  secondary: "#dd3a5a", // Emma red/pink
  primaryLight: "#4f46e5",
  secondaryLight: "#f43f5e",
  primaryDark: "#1e1b8b",
  secondaryDark: "#be185d",
};

// Función para asignar colores Emma a cada herramienta
function getToolColor(id: string): "primary" | "secondary" {
  const hashCode = id
    .split("")
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return hashCode % 2 === 0 ? "primary" : "secondary";
}

// Función para obtener el color Emma completo
function getEmmaColor(id: string, intensity: "base" | "light" | "dark" = "base"): string {
  const colorType = getToolColor(id);

  const colorMap = {
    primary: {
      base: EMMA_COLORS.primary,
      light: EMMA_COLORS.primaryLight,
      dark: EMMA_COLORS.primaryDark,
    },
    secondary: {
      base: EMMA_COLORS.secondary,
      light: EMMA_COLORS.secondaryLight,
      dark: EMMA_COLORS.secondaryDark,
    },
  };

  return colorMap[colorType][intensity];
}

function MarketingDesignToolsContent() {
  const [, setLocation] = useLocation();
  const { favorites, isFavorite, toggleFavorite, getFavoritesSorted } = useFavorites();

  // Breadcrumbs para la página de herramientas de diseño
  const breadcrumbs = [
    {
      label: 'Dashboard',
      href: '/dashboard',
      icon: <Home className="w-4 h-4" />,
    },
    {
      label: 'Herramientas de Marketing',
      href: '/dashboard/herramientas-marketing',
    },
    {
      label: 'Herramientas de Diseño',
    },
  ];

  // Filtrar solo herramientas de diseño implementadas
  const designTools = aiToolsData.filter((tool) =>
    isToolImplemented(tool.id) && tool.categoryId === "design"
  );
  return (
    <div className="space-y-10">
      {/* Breadcrumbs */}
      <Breadcrumbs items={breadcrumbs} className="mb-6" />

      {/* Back Button */}
      <div className="mb-6">
        <Button
          variant="outline"
          className="border-blue-200 bg-blue-50 hover:bg-blue-100 hover:text-blue-700 text-blue-600 transition-all"
          onClick={() => setLocation("/dashboard/herramientas-marketing")}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver a Herramientas de Marketing
        </Button>
      </div>

      {/* Hero Section - Matching main page style */}
      <div className="relative rounded-2xl overflow-hidden mb-8 backdrop-blur-xl">
        <div className="absolute inset-0 bg-gradient-to-br from-[#3018ef] via-[#4f46e5] to-[#dd3a5a] opacity-95"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        <div className="relative px-8 py-16 md:py-20 md:px-12">
          <div className="max-w-5xl">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: "easeOut" }}
              className="mb-8"
            >
              <motion.span
                className="inline-flex items-center bg-white/20 backdrop-blur-md text-white font-semibold px-6 py-3 rounded-full mb-6 border border-white/30"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Sparkles className="inline-block w-5 h-5 mr-2" />
                Herramientas de Diseño
              </motion.span>
              <h1 className="text-4xl md:text-5xl lg:text-7xl font-black text-white mb-6 leading-tight">
                Herramientas de{" "}
                <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
                  Diseño
                </span>
                <br className="hidden md:block" />
                <span className="text-white/90 font-light">
                  para crear contenido visual impactante
                </span>
              </h1>
              <p className="text-white/90 text-xl md:text-2xl max-w-3xl font-light leading-relaxed">
                Crea diseños profesionales y paletas de colores armónicas con nuestras herramientas especializadas en diseño visual.
              </p>
            </motion.div>
          </div>
        </div>

        {/* Floating elements with glassmorphism */}
        <motion.div
          className="absolute right-0 bottom-0 transform translate-y-1/4 -translate-x-10 hidden lg:block"
          initial={{ opacity: 0, x: 100, rotate: -10 }}
          animate={{ opacity: 1, x: 0, rotate: 0 }}
          transition={{ duration: 0.8, delay: 0.4, ease: "easeOut" }}
        >
          <div className="relative w-72 h-72">
            <motion.div
              className="absolute w-40 h-40 bg-white/20 backdrop-blur-md rounded-2xl -top-32 -left-20 transform rotate-12 flex items-center justify-center shadow-2xl border border-white/30"
              animate={{ rotate: [12, 18, 12] }}
              transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            >
              <span className="text-7xl">🎨</span>
            </motion.div>
            <motion.div
              className="absolute w-44 h-44 bg-white/15 backdrop-blur-md rounded-2xl -top-10 left-20 transform -rotate-6 flex items-center justify-center shadow-2xl border border-white/20"
              animate={{ rotate: [-6, -12, -6] }}
              transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 1 }}
            >
              <span className="text-7xl">🎭</span>
            </motion.div>
            <motion.div
              className="absolute w-36 h-36 bg-white/25 backdrop-blur-md rounded-2xl top-24 -left-10 transform rotate-45 flex items-center justify-center shadow-2xl border border-white/40"
              animate={{ rotate: [45, 50, 45] }}
              transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 2 }}
            >
              <span className="text-6xl">🔍</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
      {/* Herramientas Section - Matching main page style */}
      <motion.div
        className="mb-12"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-8 gap-6">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-2">
              Herramientas de Diseño Disponibles
            </h2>
            <p className="text-gray-600 text-lg">
              Herramientas especializadas para crear contenido visual impactante
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {designTools.map((tool, index) => (
            <motion.div
              key={tool.id}
              className={`group relative bg-white/90 backdrop-blur-md rounded-2xl overflow-hidden border border-white/20 hover:border-white/40 flex flex-col h-full hover:shadow-2xl transition-all duration-500 ${tool.comingSoon ? "opacity-80" : ""}`}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              whileHover={{
                y: -12,
                scale: 1.02,
                transition: { type: "spring", stiffness: 400, damping: 25 }
              }}
            >
              {tool.comingSoon && (
                <div className="absolute top-4 right-4 z-10">
                  <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white font-semibold px-3 py-1 rounded-full shadow-lg">
                    Próximamente
                  </Badge>
                </div>
              )}

              {/* Botón de favoritos - Matching main page style */}
              <motion.button
                className={`absolute top-4 ${tool.comingSoon ? 'right-28' : 'right-4'} z-20 p-3 rounded-full border-2 backdrop-blur-md ${
                  isFavorite(tool.id)
                    ? 'bg-[#dd3a5a] text-white border-[#dd3a5a] shadow-xl'
                    : 'bg-white/80 text-[#dd3a5a] border-[#dd3a5a]/30 shadow-lg hover:bg-[#dd3a5a]/10'
                } transition-all duration-300`}
                whileHover={{ scale: 1.2, rotate: 5 }}
                whileTap={{ scale: 0.9 }}
                onClick={(e) => {
                  e.stopPropagation();
                  toggleFavorite(tool.id, tool.name);
                }}
                title={isFavorite(tool.id) ? 'Quitar de favoritos' : 'Añadir a favoritos'}
              >
                <Heart
                  className="w-5 h-5"
                  fill={isFavorite(tool.id) ? 'currentColor' : 'none'}
                  strokeWidth={2}
                />
              </motion.button>

              {/* Header with Emma brand gradient */}
              <div
                className="h-48 relative overflow-hidden"
                style={{
                  background: `linear-gradient(135deg, ${getEmmaColor(tool.id, "base")}, ${getEmmaColor(tool.id, "light")})`,
                }}
              >
                {/* Glassmorphism overlay */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20"></div>

                <div className="pl-6 pt-6 text-white z-10 relative">
                  <h3 className="text-2xl font-bold mb-3 leading-tight">{tool.name}</h3>
                  <Badge
                    variant="outline"
                    className="bg-white/20 backdrop-blur-md text-white border-white/40 font-semibold px-3 py-1"
                  >
                    {tool.level}
                  </Badge>
                </div>

                {/* Icon with modern styling */}
                <motion.div
                  className="absolute right-6 bottom-6 z-10"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <div className="w-28 h-28 rounded-2xl bg-white/20 backdrop-blur-md flex items-center justify-center border border-white/30 shadow-xl">
                    <div className="text-5xl">{tool.icon}</div>
                  </div>
                </motion.div>

                {/* Modern geometric patterns */}
                <div className="absolute right-0 top-0 w-40 h-40 bg-white/5 rounded-bl-[3rem] backdrop-blur-sm"></div>
                <div className="absolute left-0 bottom-0 w-20 h-20 bg-white/10 rounded-tr-[2rem]"></div>
                <div className="absolute right-16 bottom-16 w-12 h-12 bg-white/15 rounded-full backdrop-blur-sm"></div>
                <div className="absolute left-8 top-8 w-6 h-6 bg-white/20 rounded-full"></div>
              </div>
              <div className="p-8 flex-grow relative">
                <div className="flex items-start gap-4 relative">
                  <div className="flex-grow">
                    <p className="text-gray-700 mb-6 text-base leading-relaxed">
                      {tool.description}
                    </p>

                    <div className="mb-6">
                      <span className="text-base font-semibold text-gray-800 mb-4 block">
                        Con esta herramienta podrás:
                      </span>
                      <ul className="space-y-3">
                        {tool.features.slice(0, 3).map((feature, idx) => (
                          <motion.li
                            key={idx}
                            className="text-sm flex items-start"
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.3, delay: idx * 0.1 }}
                          >
                            <div
                              className="min-w-[20px] h-[20px] rounded-full flex items-center justify-center mt-0.5 mr-3 shadow-sm"
                              style={{
                                background: `linear-gradient(135deg, ${getEmmaColor(tool.id, "base")}20, ${getEmmaColor(tool.id, "light")}20)`,
                                border: `1px solid ${getEmmaColor(tool.id, "base")}30`,
                              }}
                            >
                              <ChevronRight
                                size={12}
                                style={{
                                  color: getEmmaColor(tool.id, "base"),
                                }}
                              />
                            </div>
                            <span className="text-gray-600 leading-relaxed">{feature}</span>
                          </motion.li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              {/* Modern button with glassmorphism */}
              <div className="p-6 border-t border-gray-100/50 relative z-10">
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    className={`w-full py-4 text-white font-semibold rounded-xl text-lg shadow-xl hover:shadow-2xl transition-all duration-300 border-none`}
                    style={{
                      background: tool.comingSoon
                        ? 'linear-gradient(135deg, #9ca3af, #6b7280)'
                        : `linear-gradient(135deg, ${getEmmaColor(tool.id, "base")}, ${getEmmaColor(tool.id, "light")})`,
                    }}
                    disabled={tool.comingSoon}
                    onClick={() => {
                      if (tool.comingSoon) return;
                      setLocation(getToolRoute(tool.id));
                    }}
                  >
                    {tool.comingSoon ? "Próximamente" : "Usar Herramienta"}{" "}
                    <ArrowRight size={18} className="ml-2" />
                  </Button>
                </motion.div>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-transparent via-transparent to-black/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
}
function MarketingDesignToolsPage() {
  return (
    <DashboardLayoutWrapper pageTitle="Herramientas de Diseño">
      <MarketingDesignToolsContent />
    </DashboardLayoutWrapper>
  );
}

export default MarketingDesignToolsPage;
