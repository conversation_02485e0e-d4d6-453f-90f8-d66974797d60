#!/usr/bin/env python3
import shutil
import os

# Rutas de origen y destino
source = "/Users/<USER>/Desktop/emma-studio--main/Imagen infogra.png"
destination = "/Users/<USER>/Desktop/emma-studio--main/client/public/imagen-infogra.png"

try:
    # Copiar la imagen
    shutil.copy2(source, destination)
    print(f"✅ Imagen copiada exitosamente de {source} a {destination}")
    
    # Verificar que el archivo existe
    if os.path.exists(destination):
        size = os.path.getsize(destination)
        print(f"✅ Archivo verificado: {size} bytes")
    else:
        print("❌ Error: El archivo no se copió correctamente")
        
except Exception as e:
    print(f"❌ Error al copiar la imagen: {e}")
