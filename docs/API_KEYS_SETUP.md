# Emma Studio API Keys Setup Guide

This guide will help you obtain and configure all the API keys required for Emma Studio's AI and web services.

## 🔑 Required API Keys

### 1. Google Gemini API Key (Required)

**Purpose**: Core AI functionality, content generation, and optimization

**How to get it**:
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key (starts with `AIza`)

**Configuration**:
```env
GEMINI_API_KEY=AIzaSyC...your-key-here
VITE_GEMINI_API_KEY=${GEMINI_API_KEY}
```

**Cost**: Free tier available with generous limits

### 2. Stability AI API Key (Required)

**Purpose**: Image generation and editing

**How to get it**:
1. Go to [Stability AI Platform](https://platform.stability.ai/account/keys)
2. Create an account or sign in
3. Navigate to "API Keys" section
4. Click "Create New Key"
5. Copy the generated key (starts with `sk-`)

**Configuration**:
```env
STABILITY_API_KEY=sk-...your-key-here
VITE_STABILITY_API_KEY=${STABILITY_API_KEY}
```

**Cost**: Pay-per-use, $10 credit for new accounts

### 3. OpenAI API Key (Required)

**Purpose**: Advanced text generation and analysis

**How to get it**:
1. Go to [OpenAI Platform](https://platform.openai.com/api-keys)
2. Create an account or sign in
3. Click "Create new secret key"
4. Copy the generated key (starts with `sk-`)

**Configuration**:
```env
OPENAI_API_KEY=sk-...your-key-here
VITE_OPENAI_API_KEY=${OPENAI_API_KEY}
```

**Cost**: Pay-per-use, $5 free credit for new accounts

## 🔧 Optional API Keys

### 4. ElevenLabs API Key (Optional)

**Purpose**: Voice synthesis and audio generation

**How to get it**:
1. Go to [ElevenLabs](https://elevenlabs.io/)
2. Create an account
3. Go to Profile → API Keys
4. Generate a new API key

**Configuration**:
```env
ELEVENLABS_API_KEY=your-key-here
VITE_ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
```

**Cost**: Free tier available, paid plans for more usage

### 5. Google Cloud API Key (Optional)

**Purpose**: Search functionality and additional Google services

**How to get it**:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable required APIs:
   - Custom Search API
   - PageSpeed Insights API
4. Go to "Credentials" → "Create Credentials" → "API Key"
5. Copy the generated key

**Additional Setup**:
- Create a Custom Search Engine at [Google CSE](https://cse.google.com/)
- Note your Search Engine ID

**Configuration**:
```env
GOOGLE_API_KEY=AIza...your-key-here
GOOGLE_CSE_ID=your-search-engine-id
```

**Cost**: Free tier available with daily limits

### 6. Serper API Key (Optional)

**Purpose**: SEO analysis and SERP data

**How to get it**:
1. Go to [Serper.dev](https://serper.dev/)
2. Sign up for an account
3. Go to Dashboard → API Keys
4. Copy your API key

**Configuration**:
```env
SERPER_API_KEY=your-key-here
```

**Cost**: Free tier with 2,500 searches, paid plans available

### 7. Polotno Studio API Key (Optional)

**Purpose**: Advanced visual editor functionality

**How to get it**:
1. Go to [Polotno Cabinet](https://polotno.com/cabinet)
2. Create an account
3. Add your domain to allowed domains
4. Copy the generated API key

**Configuration**:
```env
VITE_POLOTNO_API_KEY=your-key-here
```

**Cost**: Free tier available, paid plans for commercial use

### 8. Browserless API Key (Optional)

**Purpose**: Web automation and screenshot generation

**How to get it**:
1. Go to [Browserless.io](https://www.browserless.io/)
2. Create an account
3. Go to Dashboard → API Keys
4. Copy your API key

**Configuration**:
```env
BROWSERLESS_API_KEY=your-key-here
```

**Cost**: Free tier available, paid plans for higher usage

## 🗄️ Database Configuration

### Supabase Setup (Required)

**Purpose**: Main database for Emma Studio

**How to set it up**:
1. Go to [Supabase](https://supabase.com/)
2. Create a new project
3. Wait for project initialization
4. Go to Settings → Database
5. Copy the connection string

**Configuration**:
```env
DATABASE_URL=postgresql://postgres:[password]@[host]:[port]/[database]
```

**Additional Setup**:
1. Go to SQL Editor in Supabase dashboard
2. Copy and run the contents of `supabase-schema.sql`
3. Verify tables are created

**Cost**: Free tier available with generous limits

## 📝 Environment File Setup

### 1. Root .env File

Create `.env` in the project root:

```env
# Core AI APIs
GEMINI_API_KEY=your_gemini_api_key_here
VITE_GEMINI_API_KEY=${GEMINI_API_KEY}
STABILITY_API_KEY=your_stability_api_key_here
VITE_STABILITY_API_KEY=${STABILITY_API_KEY}
OPENAI_API_KEY=your_openai_api_key_here
VITE_OPENAI_API_KEY=${OPENAI_API_KEY}

# Optional APIs
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
VITE_ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here
SERPER_API_KEY=your_serper_api_key_here
VITE_POLOTNO_API_KEY=your_polotno_api_key_here
BROWSERLESS_API_KEY=your_browserless_api_key_here

# Database
DATABASE_URL=postgresql://postgres:password@host:port/database

# Security
SECRET_KEY=your-secure-secret-key-at-least-32-characters-long
JWT_SECRET=your-secure-jwt-secret-at-least-32-characters-long

# Development
DEBUG=true
LOG_LEVEL=INFO
NODE_ENV=development
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
```

### 2. Backend .env File

Create `backend/.env`:

```env
# Core AI APIs
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
STABILITY_API_KEY=your_stability_api_key_here
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Search APIs
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here
SERPER_API_KEY=your_serper_api_key_here

# Web Automation
BROWSERLESS_API_KEY=your_browserless_api_key_here

# Database
DATABASE_URL=postgresql://postgres:password@host:port/database

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Development
DEBUG=true
LOG_LEVEL=INFO
```

## 🔒 Security Best Practices

### 1. Keep API Keys Secret
- Never commit API keys to version control
- Use environment variables only
- Don't share keys in screenshots or logs

### 2. Use Different Keys for Different Environments
- Development keys for local development
- Production keys for deployed applications
- Staging keys for testing environments

### 3. Monitor API Usage
- Set up billing alerts
- Monitor usage dashboards
- Implement rate limiting in your application

### 4. Rotate Keys Regularly
- Change API keys periodically
- Revoke unused keys
- Use key management services for production

## 🧪 Testing Your Configuration

### 1. Run the Verification Script
```bash
./scripts/verify-setup.sh
```

### 2. Test Individual APIs

#### Test Gemini API
```bash
curl -X POST \
  'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"contents":[{"parts":[{"text":"Hello"}]}]}'
```

#### Test Stability AI
```bash
curl -X POST \
  'https://api.stability.ai/v1/generation/stable-diffusion-xl-1024-v1-0/text-to-image' \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"text_prompts":[{"text":"test"}],"cfg_scale":7,"height":1024,"width":1024,"samples":1,"steps":30}'
```

#### Test OpenAI API
```bash
curl -X POST \
  'https://api.openai.com/v1/chat/completions' \
  -H 'Authorization: Bearer YOUR_API_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"model":"gpt-3.5-turbo","messages":[{"role":"user","content":"Hello"}],"max_tokens":5}'
```

### 3. Test Database Connection
```bash
cd backend
poetry run python -c "
import asyncio
from app.core.database import engine
async def test():
    async with engine.begin() as conn:
        result = await conn.execute('SELECT 1')
        print('Database connection successful')
asyncio.run(test())
"
```

## 💰 Cost Management

### Free Tier Limits (Approximate)
- **Gemini**: 60 requests per minute
- **Stability AI**: $10 free credit
- **OpenAI**: $5 free credit
- **ElevenLabs**: 10,000 characters/month
- **Google Cloud**: 100 searches/day
- **Serper**: 2,500 searches total
- **Supabase**: 500MB database, 2GB bandwidth

### Cost Optimization Tips
1. **Cache responses** when possible
2. **Implement rate limiting** to avoid overuse
3. **Monitor usage** regularly
4. **Use free tiers** for development
5. **Optimize prompts** to reduce token usage

## 🆘 Troubleshooting

### Common Issues

#### API Key Not Working
1. Check key format (correct prefix)
2. Verify key is active in provider dashboard
3. Check billing status
4. Ensure proper permissions

#### Environment Variables Not Loading
1. Restart development server
2. Check file names (`.env` not `.env.txt`)
3. Verify variable names (VITE_ prefix for frontend)
4. Check for syntax errors in .env files

#### Database Connection Issues
1. Verify Supabase project is active
2. Check connection string format
3. Test network connectivity
4. Verify database schema is applied

For more troubleshooting help, see [TROUBLESHOOTING.md](TROUBLESHOOTING.md).

## ✅ Checklist

Before starting development, ensure you have:

- [ ] Gemini API key configured
- [ ] Stability AI API key configured  
- [ ] OpenAI API key configured
- [ ] Supabase database set up
- [ ] Environment files created
- [ ] API keys tested
- [ ] Database schema applied
- [ ] Verification script passes

Once all required keys are configured, you're ready to start developing with Emma Studio! 🚀
