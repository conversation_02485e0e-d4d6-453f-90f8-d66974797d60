# Emma Studio Troubleshooting Guide

This guide covers common issues you might encounter while setting up or developing with Emma Studio, along with their solutions.

## 🚨 Quick Fixes

### Setup Script Issues

#### Permission Denied
```bash
# Make setup script executable
chmod +x setup-dev-environment.sh
chmod +x scripts/verify-setup.sh
```

#### Script Fails on macOS
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install Homebrew if missing
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```

## 🔧 Installation Issues

### Node.js Issues

#### Wrong Node.js Version
```bash
# Using nvm (recommended)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install 18
nvm use 18

# Using Homebrew (macOS)
brew uninstall node
brew install node@18
brew link node@18

# Using apt (Ubuntu/Debian)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

#### npm Permission Issues (Linux/macOS)
```bash
# Fix npm permissions
mkdir ~/.npm-global
npm config set prefix '~/.npm-global'
echo 'export PATH=~/.npm-global/bin:$PATH' >> ~/.bashrc
source ~/.bashrc
```

### Python Issues

#### Python Version Issues
```bash
# Install Python 3.11 (recommended)
# macOS
brew install python@3.11

# Ubuntu/Debian
sudo apt-get install python3.11 python3.11-venv python3.11-pip

# Update alternatives (Linux)
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.11 1
```

#### Poetry Installation Issues
```bash
# Remove existing Poetry installation
rm -rf ~/.local/share/pypoetry

# Reinstall Poetry
curl -sSL https://install.python-poetry.org | python3 -

# Add to PATH
echo 'export PATH="$HOME/.local/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Verify installation
poetry --version
```

### Docker Issues

#### Docker Not Running
```bash
# Linux
sudo systemctl start docker
sudo systemctl enable docker

# Add user to docker group
sudo usermod -aG docker $USER
# Log out and back in for changes to take effect
```

#### Docker Permission Denied
```bash
# Linux - Add user to docker group
sudo usermod -aG docker $USER
newgrp docker

# Test docker without sudo
docker run hello-world
```

## 🌐 Network and API Issues

### API Key Issues

#### Invalid API Keys
1. **Verify API Key Format**:
   - Gemini: Should start with `AIza`
   - OpenAI: Should start with `sk-`
   - Stability AI: Should start with `sk-`

2. **Check API Key Permissions**:
   - Ensure keys have necessary permissions
   - Check usage limits and quotas
   - Verify billing is set up if required

3. **Environment Variable Issues**:
   ```bash
   # Check if variables are loaded
   echo $GEMINI_API_KEY
   
   # Restart terminal after editing .env
   source ~/.bashrc
   ```

#### API Rate Limiting
```bash
# Check API usage in respective dashboards
# Implement exponential backoff in code
# Consider upgrading API plans
```

### Database Issues

#### Supabase Connection Issues
1. **Check Database URL Format**:
   ```
   postgresql://postgres:[password]@[host]:[port]/[database]
   ```

2. **Verify Supabase Project Status**:
   - Check if project is paused
   - Verify network restrictions
   - Check connection pooling settings

3. **Test Connection**:
   ```bash
   # Test with psql
   psql "postgresql://postgres:password@host:port/database"
   ```

#### Database Schema Issues
```sql
-- Check if tables exist
\dt

-- Recreate schema if needed
-- Copy and run supabase-schema.sql in Supabase SQL editor
```

## 🚀 Development Server Issues

### Port Already in Use

#### Find and Kill Process
```bash
# Find process using port
lsof -i :8000  # Backend
lsof -i :5173  # Frontend
lsof -i :6333  # Qdrant

# Kill specific process
kill -9 <PID>

# Kill all node processes (use with caution)
pkill -f node
```

#### Use Different Ports
```bash
# Backend
cd backend
poetry run uvicorn app.main:app --reload --port 8001

# Frontend
cd client
npm run dev -- --port 5174
```

### Backend Issues

#### Import Errors
```bash
cd backend

# Check Python path
poetry run python -c "import sys; print(sys.path)"

# Reinstall dependencies
poetry install --no-cache

# Clear Poetry cache
poetry cache clear pypi --all
```

#### FastAPI Import Issues
```bash
# Check FastAPI installation
poetry run python -c "import fastapi; print(fastapi.__version__)"

# Reinstall FastAPI
poetry remove fastapi
poetry add fastapi==0.95.2
```

#### Database Connection Errors
```bash
# Check environment variables
poetry run python -c "import os; print(os.getenv('DATABASE_URL'))"

# Test database connection
poetry run python -c "
import asyncio
from app.core.database import engine
async def test():
    async with engine.begin() as conn:
        result = await conn.execute('SELECT 1')
        print('Database connection successful')
asyncio.run(test())
"
```

### Frontend Issues

#### Vite Build Errors
```bash
cd client

# Clear cache
rm -rf node_modules/.vite
rm -rf dist

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check for TypeScript errors
npx tsc --noEmit
```

#### Module Resolution Issues
```bash
# Check TypeScript configuration
cat tsconfig.json

# Verify imports
npx tsc --listFiles | grep "your-module"

# Clear TypeScript cache
rm -rf node_modules/.cache
```

#### Environment Variable Issues
```bash
# Check Vite environment variables (must start with VITE_)
echo $VITE_GEMINI_API_KEY

# Restart development server after changing .env
npm run dev
```

## 🐳 Docker Issues

### Container Issues

#### Container Won't Start
```bash
# Check container logs
docker logs qdrant

# Remove and recreate container
docker rm -f qdrant
docker run -d --name qdrant -p 6333:6333 qdrant/qdrant
```

#### Port Conflicts
```bash
# Use different port
docker run -d --name qdrant -p 6334:6333 qdrant/qdrant

# Update application configuration to use new port
```

### Docker Compose Issues

#### Build Failures
```bash
# Clean build
docker-compose down
docker system prune -a
docker-compose up --build --force-recreate
```

#### Volume Issues
```bash
# Reset volumes
docker-compose down -v
docker-compose up
```

## 💾 Performance Issues

### Slow Development Server

#### Node.js Memory Issues
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Add to .bashrc for persistence
echo 'export NODE_OPTIONS="--max-old-space-size=4096"' >> ~/.bashrc
```

#### File Watching Issues
```bash
# Increase file watch limit (Linux)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Exclude node_modules from antivirus scanning
```

### Database Performance

#### Slow Queries
```sql
-- Check active connections
SELECT * FROM pg_stat_activity;

-- Check slow queries
SELECT query, mean_exec_time, calls 
FROM pg_stat_statements 
ORDER BY mean_exec_time DESC 
LIMIT 10;
```

## 🔍 Debugging Tips

### Enable Debug Logging

#### Backend Debug Mode
```bash
# Set in backend/.env
DEBUG=true
LOG_LEVEL=DEBUG

# Or run with debug
cd backend
poetry run uvicorn app.main:app --reload --log-level debug
```

#### Frontend Debug Mode
```bash
# Set in .env
VITE_DEBUG=true

# Enable React DevTools
# Install React Developer Tools browser extension
```

### Check Service Health

#### Backend Health Check
```bash
curl http://localhost:8000/health
```

#### Frontend Health Check
```bash
curl http://localhost:5173
```

#### Qdrant Health Check
```bash
curl http://localhost:6333/health
```

### Common Error Messages

#### "Module not found"
1. Check if dependency is installed
2. Verify import path
3. Check TypeScript configuration
4. Clear cache and reinstall

#### "Permission denied"
1. Check file permissions
2. Run with appropriate user
3. Check Docker permissions
4. Verify API key permissions

#### "Connection refused"
1. Check if service is running
2. Verify port configuration
3. Check firewall settings
4. Test network connectivity

## 🆘 Getting Additional Help

### Before Asking for Help

1. **Run the verification script**:
   ```bash
   ./scripts/verify-setup.sh
   ```

2. **Check logs**:
   ```bash
   # Backend logs
   cd backend && poetry run uvicorn app.main:app --reload
   
   # Frontend logs
   cd client && npm run dev
   
   # Docker logs
   docker logs qdrant
   ```

3. **Gather system information**:
   ```bash
   # System info
   uname -a
   node --version
   python3 --version
   poetry --version
   docker --version
   ```

### Where to Get Help

1. **Documentation**: Check `docs/DEVELOPER_SETUP.md`
2. **GitHub Issues**: Search existing issues or create a new one
3. **Discussions**: Use GitHub Discussions for questions
4. **Community**: Join our Discord server (if available)

### Creating a Good Bug Report

Include:
- Operating system and version
- Node.js and Python versions
- Complete error messages
- Steps to reproduce
- What you expected to happen
- What actually happened
- What you've already tried

### Example Bug Report Template

```markdown
**Environment:**
- OS: macOS 13.0
- Node.js: v18.17.0
- Python: 3.11.5
- Poetry: 1.6.1

**Issue:**
Backend fails to start with import error

**Error Message:**
```
ModuleNotFoundError: No module named 'app.core.config'
```

**Steps to Reproduce:**
1. Run `poetry install` in backend directory
2. Run `poetry run uvicorn app.main:app --reload`
3. Error occurs immediately

**Expected:** Backend should start successfully
**Actual:** Import error prevents startup

**Tried:**
- Reinstalled dependencies
- Checked Python path
- Verified file exists
```

This troubleshooting guide should help you resolve most common issues. If you encounter a problem not covered here, please contribute by adding the solution once you find it!
