# Emma Studio Runtime Guide

This guide covers everything you need to know about running Emma Studio in development mode on a daily basis.

## 🚀 Quick Start Commands

### Option 1: Start Everything (Recommended)
```bash
# Start both backend and frontend together
npm run dev
```

### Option 2: Start Services Individually
```bash
# Terminal 1 - Backend
npm run dev:backend

# Terminal 2 - Frontend  
npm run dev:client
```

### Option 3: Manual Control
```bash
# Terminal 1 - Backend
cd backend
poetry run uvicorn app.main:app --reload --port 8000

# Terminal 2 - Frontend
cd client
npm run dev

# Terminal 3 - Qdrant (if needed)
docker run -d --name qdrant -p 6333:6333 qdrant/qdrant
```

## 🌐 Port Information

| Service | Port | URL | Purpose |
|---------|------|-----|---------|
| **Frontend** | 5173 | http://localhost:5173 | React development server |
| **Backend** | 8000 | http://localhost:8000 | FastAPI application |
| **Backend Docs** | 8000 | http://localhost:8000/docs | Interactive API documentation |
| **Qdrant** | 6333 | http://localhost:6333 | Vector database |
| **Qdrant Dashboard** | 6333 | http://localhost:6333/dashboard | Qdrant web interface |

### Port Conflicts
If default ports are in use, you can specify alternatives:

```bash
# Backend on different port
cd backend
poetry run uvicorn app.main:app --reload --port 8001

# Frontend on different port
cd client
npm run dev -- --port 5174
```

## 🔗 Service Dependencies

### Startup Order
1. **Qdrant** (optional) - Vector database
2. **Backend** - FastAPI server
3. **Frontend** - React development server

### Required Services
- ✅ **Backend**: Always required
- ✅ **Frontend**: Always required
- ⚠️ **Qdrant**: Optional (only needed for vector operations)

### Optional Services
- **Database**: Supabase (cloud-hosted, always available)
- **External APIs**: Configured via environment variables

## ✅ Runtime Verification

### Quick Health Check
```bash
# Check all services at once
curl -s http://localhost:8000/health && \
curl -s http://localhost:5173 > /dev/null && \
curl -s http://localhost:6333/health > /dev/null && \
echo "✅ All services are running"
```

### Individual Service Checks

#### Backend Health Check
```bash
# Basic health check
curl http://localhost:8000/health

# Expected response: {"status": "healthy"}

# API documentation
open http://localhost:8000/docs  # macOS
# or visit http://localhost:8000/docs in browser
```

#### Frontend Health Check
```bash
# Check if frontend is responding
curl -I http://localhost:5173

# Expected: HTTP/1.1 200 OK

# Open in browser
open http://localhost:5173  # macOS
# or visit http://localhost:5173 in browser
```

#### Qdrant Health Check
```bash
# Check Qdrant status
curl http://localhost:6333/health

# Expected response: {"status": "ok"}

# Check collections
curl http://localhost:6333/collections
```

### Process Verification
```bash
# Check what's running on each port
lsof -i :8000  # Backend
lsof -i :5173  # Frontend
lsof -i :6333  # Qdrant

# Check all Emma Studio processes
ps aux | grep -E "(uvicorn|vite|qdrant)"
```

## 🔧 Development Workflow

### Starting Your Day
```bash
# 1. Navigate to project directory
cd /path/to/emma-studio

# 2. Pull latest changes
git pull origin main

# 3. Update dependencies (if needed)
cd backend && poetry install && cd ..
cd client && npm install && cd ..

# 4. Start all services
npm run dev

# 5. Verify everything is running
./scripts/verify-setup.sh
```

### During Development

#### Restarting Services
```bash
# Restart backend only (Ctrl+C then restart)
npm run dev:backend

# Restart frontend only (Ctrl+C then restart)
npm run dev:client

# Restart everything
# Ctrl+C to stop, then:
npm run dev
```

#### Viewing Logs

**Backend Logs:**
```bash
# If using npm script
npm run dev:backend

# If running manually
cd backend
poetry run uvicorn app.main:app --reload --port 8000 --log-level debug
```

**Frontend Logs:**
```bash
# Vite shows logs in terminal automatically
npm run dev:client
```

**Docker Logs:**
```bash
# Qdrant logs
docker logs qdrant

# Follow logs in real-time
docker logs -f qdrant
```

#### Hot Reloading
- **Backend**: Automatic reload on Python file changes
- **Frontend**: Automatic reload on TypeScript/React file changes
- **Environment**: Restart required for .env changes

### Stopping Services

#### Graceful Shutdown
```bash
# Stop all services (if started with npm run dev)
Ctrl+C

# Stop individual services
Ctrl+C in respective terminals
```

#### Force Stop
```bash
# Kill processes by port
lsof -ti:8000 | xargs kill -9  # Backend
lsof -ti:5173 | xargs kill -9  # Frontend
lsof -ti:6333 | xargs kill -9  # Qdrant

# Stop Docker containers
docker stop qdrant
docker rm qdrant
```

## 🐛 Common Runtime Issues

### Port Already in Use

**Problem**: `Error: listen EADDRINUSE: address already in use :::8000`

**Solutions:**
```bash
# Find what's using the port
lsof -i :8000

# Kill the process
kill -9 <PID>

# Or use a different port
cd backend
poetry run uvicorn app.main:app --reload --port 8001
```

### Backend Won't Start

**Problem**: Import errors or dependency issues

**Solutions:**
```bash
# Check Python environment
cd backend
poetry env info

# Reinstall dependencies
poetry install --no-cache

# Check for syntax errors
poetry run python -m py_compile app/main.py

# Run with verbose logging
poetry run uvicorn app.main:app --reload --log-level debug
```

### Frontend Won't Start

**Problem**: Node.js or dependency issues

**Solutions:**
```bash
# Clear cache and reinstall
cd client
rm -rf node_modules package-lock.json
npm install

# Check for TypeScript errors
npx tsc --noEmit

# Clear Vite cache
rm -rf node_modules/.vite

# Run with verbose output
npm run dev -- --debug
```

### Database Connection Issues

**Problem**: Cannot connect to Supabase

**Solutions:**
```bash
# Check environment variables
echo $DATABASE_URL

# Test connection
cd backend
poetry run python -c "
import asyncio
from app.core.database import engine
async def test():
    try:
        async with engine.begin() as conn:
            await conn.execute('SELECT 1')
        print('✅ Database connection successful')
    except Exception as e:
        print(f'❌ Database connection failed: {e}')
asyncio.run(test())
"
```

### Qdrant Connection Issues

**Problem**: Vector database not responding

**Solutions:**
```bash
# Check if container is running
docker ps | grep qdrant

# Restart Qdrant
docker restart qdrant

# Check logs
docker logs qdrant

# Recreate container
docker rm -f qdrant
docker run -d --name qdrant -p 6333:6333 qdrant/qdrant
```

### API Key Issues

**Problem**: External API calls failing

**Solutions:**
```bash
# Check environment variables are loaded
echo $GEMINI_API_KEY
echo $STABILITY_API_KEY

# Restart services after changing .env
# (Environment variables are loaded at startup)

# Test API keys manually
curl -X POST \
  'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=YOUR_KEY' \
  -H 'Content-Type: application/json' \
  -d '{"contents":[{"parts":[{"text":"test"}]}]}'
```

## 📊 Monitoring Application Health

### Real-time Monitoring
```bash
# Watch backend logs
tail -f backend/app.log

# Monitor system resources
top -p $(pgrep -f "uvicorn\|node")

# Watch network connections
netstat -tulpn | grep -E "(8000|5173|6333)"
```

### Performance Monitoring
```bash
# Check memory usage
ps aux --sort=-%mem | grep -E "(uvicorn|node|qdrant)"

# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8000/health

# Create curl-format.txt:
echo "     time_namelookup:  %{time_namelookup}\n
        time_connect:  %{time_connect}\n
     time_appconnect:  %{time_appconnect}\n
    time_pretransfer:  %{time_pretransfer}\n
       time_redirect:  %{time_redirect}\n
  time_starttransfer:  %{time_starttransfer}\n
                     ----------\n
          time_total:  %{time_total}\n" > curl-format.txt
```

## 🔄 Daily Development Routine

### Morning Startup
```bash
# Quick start routine
cd emma-studio
git pull origin main
npm run dev
```

### During Development
```bash
# Make changes to code
# Services auto-reload

# Check logs if issues occur
# Backend: Check terminal running backend
# Frontend: Check browser console + terminal

# Test changes
open http://localhost:5173
```

### End of Day
```bash
# Stop all services
Ctrl+C

# Commit changes
git add .
git commit -m "Your changes"
git push origin your-branch
```

## 🆘 Emergency Procedures

### Complete Reset
```bash
# Stop everything
pkill -f "uvicorn\|vite\|node.*dev"
docker stop qdrant

# Clear all caches
cd backend && poetry cache clear pypi --all && cd ..
cd client && rm -rf node_modules/.vite && cd ..

# Restart everything
npm run dev
```

### Service Recovery
```bash
# If one service fails, restart just that service
# Backend only:
npm run dev:backend

# Frontend only:
npm run dev:client

# Qdrant only:
docker restart qdrant
```

## 📚 Quick Reference

### Essential Commands
```bash
npm run dev              # Start everything
npm run dev:backend      # Backend only
npm run dev:client       # Frontend only
./scripts/verify-setup.sh # Health check
```

### Essential URLs
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000
- API Docs: http://localhost:8000/docs
- Qdrant: http://localhost:6333/dashboard

### Essential Ports
- 5173: Frontend
- 8000: Backend
- 6333: Qdrant

## 📊 Development Workflow Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Morning       │    │   Development   │    │   End of Day    │
│   Startup       │    │   Cycle         │    │   Shutdown      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ git pull        │    │ Edit code       │    │ Ctrl+C          │
│ npm run dev     │    │ (auto-reload)   │    │ git commit      │
│ verify health   │    │ Test changes    │    │ git push        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌─────────────────┐
│ ✅ All services │    │ 🔄 Restart if   │
│    running      │    │    needed       │
└─────────────────┘    └─────────────────┘
```

## 🎯 Service Dependencies Flow

```
Qdrant (Optional)
    ↓
Backend (Required) ← Environment Variables
    ↓
Frontend (Required) ← Backend API
    ↓
Browser (localhost:5173)
```

## 🔧 Troubleshooting Flow

```
Issue Detected
    ↓
Run ./scripts/runtime-check.sh
    ↓
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Service Down?   │    │ Port Conflict?  │    │ Config Issue?   │
│ → Restart       │    │ → Kill Process  │    │ → Check .env    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
    ↓                       ↓                       ↓
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ npm run dev     │    │ lsof -i :PORT   │    │ Restart services│
│                 │    │ kill -9 PID     │    │ after changes   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

This runtime guide should be your go-to reference for daily Emma Studio development! 🚀
