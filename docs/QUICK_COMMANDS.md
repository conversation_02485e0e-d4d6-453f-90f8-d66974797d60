# Emma Studio Quick Commands Reference

A cheat sheet for daily Emma Studio development commands.

## 🚀 Starting Services

```bash
# Start everything (recommended)
npm run dev

# Start backend only
npm run dev:backend

# Start frontend only  
npm run dev:client

# Manual backend start
cd backend && poetry run uvicorn app.main:app --reload --port 8000

# Manual frontend start
cd client && npm run dev

# Start Qdrant (optional)
docker run -d --name qdrant -p 6333:6333 qdrant/qdrant
```

## 🔍 Health Checks

```bash
# Complete health check
./scripts/runtime-check.sh

# Quick service verification
curl http://localhost:8000/health  # Backend
curl http://localhost:5173         # Frontend  
curl http://localhost:6333/health  # Qdrant

# Check what's running on ports
lsof -i :8000  # Backend
lsof -i :5173  # Frontend
lsof -i :6333  # Qdrant
```

## 🛑 Stopping Services

```bash
# Stop all (if started with npm run dev)
Ctrl+C

# Kill by port
lsof -ti:8000 | xargs kill -9  # Backend
lsof -ti:5173 | xargs kill -9  # Frontend
lsof -ti:6333 | xargs kill -9  # Qdrant

# Stop Docker containers
docker stop qdrant
docker rm qdrant

# Emergency stop all Emma processes
pkill -f "uvicorn\|vite\|node.*dev"
```

## 🔄 Restarting Services

```bash
# Restart everything
Ctrl+C
npm run dev

# Restart backend only
Ctrl+C (in backend terminal)
npm run dev:backend

# Restart frontend only
Ctrl+C (in frontend terminal)
npm run dev:client

# Restart Qdrant
docker restart qdrant
```

## 📊 Monitoring & Logs

```bash
# View backend logs (if running manually)
cd backend
poetry run uvicorn app.main:app --reload --log-level debug

# View Docker logs
docker logs qdrant
docker logs -f qdrant  # Follow logs

# Monitor processes
ps aux | grep -E "(uvicorn|vite|qdrant)"

# Monitor memory usage
ps aux --sort=-%mem | grep -E "(uvicorn|node|qdrant)"

# Watch network connections
netstat -tulpn | grep -E "(8000|5173|6333)"
```

## 🧹 Cleaning & Resetting

```bash
# Clear frontend cache
cd client
rm -rf node_modules/.vite
rm -rf dist

# Clear backend cache
cd backend
poetry cache clear pypi --all
find . -name "__pycache__" -type d -exec rm -rf {} +

# Reinstall dependencies
cd client && npm install && cd ..
cd backend && poetry install && cd ..

# Emergency reset
./scripts/runtime-check.sh --reset
```

## 🔧 Development Workflow

```bash
# Daily startup
cd emma-studio
git pull origin main
npm run dev

# Check everything is working
./scripts/runtime-check.sh

# Make changes (auto-reload enabled)
# Edit files in backend/ or client/

# Test changes
open http://localhost:5173

# End of day
Ctrl+C
git add .
git commit -m "Your changes"
git push origin your-branch
```

## 🐛 Troubleshooting

```bash
# Port conflicts
lsof -i :PORT_NUMBER
kill -9 PID

# Service not responding
curl -I http://localhost:PORT

# Check environment variables
echo $GEMINI_API_KEY
echo $DATABASE_URL

# Test database connection
cd backend
poetry run python -c "
import asyncio
from app.core.database import engine
async def test():
    async with engine.begin() as conn:
        await conn.execute('SELECT 1')
    print('Database OK')
asyncio.run(test())
"

# Verify setup
./scripts/verify-setup.sh
```

## 🌐 URLs & Endpoints

```bash
# Main application
http://localhost:5173

# Backend API
http://localhost:8000

# API documentation
http://localhost:8000/docs

# Qdrant dashboard
http://localhost:6333/dashboard

# Health endpoints
http://localhost:8000/health
http://localhost:6333/health
```

## 📦 Dependency Management

```bash
# Frontend dependencies
cd client
npm install package-name
npm uninstall package-name
npm update

# Backend dependencies  
cd backend
poetry add package-name
poetry remove package-name
poetry update

# Check for outdated packages
cd client && npm outdated && cd ..
cd backend && poetry show --outdated && cd ..
```

## 🔑 Environment Variables

```bash
# Check if variables are loaded
echo $GEMINI_API_KEY
echo $STABILITY_API_KEY
echo $OPENAI_API_KEY

# Reload environment (restart services)
Ctrl+C
npm run dev

# Edit environment files
nano .env
nano backend/.env
```

## 🐳 Docker Commands

```bash
# Start Qdrant
docker run -d --name qdrant -p 6333:6333 qdrant/qdrant

# Check container status
docker ps
docker ps -a

# Container management
docker start qdrant
docker stop qdrant
docker restart qdrant
docker rm qdrant

# View logs
docker logs qdrant
docker logs -f qdrant

# Clean up
docker system prune
```

## 🧪 Testing

```bash
# Backend tests
cd backend
poetry run pytest
poetry run pytest --cov=app tests/

# Frontend tests (if configured)
cd client
npm test
npm run test:coverage

# API testing
curl -X POST http://localhost:8000/api/endpoint \
  -H "Content-Type: application/json" \
  -d '{"key": "value"}'
```

## 🔍 Debugging

```bash
# Enable debug mode
# Add to .env files:
DEBUG=true
LOG_LEVEL=DEBUG

# Backend debug
cd backend
poetry run uvicorn app.main:app --reload --log-level debug

# Check Python imports
cd backend
poetry run python -c "import app.main"

# Check TypeScript compilation
cd client
npx tsc --noEmit

# Network debugging
curl -v http://localhost:8000/health
```

## 📱 Quick Status Check

```bash
# One-liner status check
curl -s http://localhost:8000/health && curl -s http://localhost:5173 > /dev/null && curl -s http://localhost:6333/health > /dev/null && echo "✅ All services running" || echo "❌ Some services down"

# Process check
ps aux | grep -E "(uvicorn|vite|qdrant)" | grep -v grep && echo "✅ Processes running" || echo "❌ No processes found"

# Port check
(lsof -i :8000 && lsof -i :5173) > /dev/null && echo "✅ Ports active" || echo "❌ Ports not active"
```

## 🆘 Emergency Commands

```bash
# Kill everything Emma-related
pkill -f "uvicorn.*emma"
pkill -f "vite.*emma"
pkill -f "node.*emma"
docker stop $(docker ps -q --filter "name=qdrant")

# Nuclear option (use with caution)
pkill -f "uvicorn"
pkill -f "vite"
pkill node
docker stop $(docker ps -q)

# Clean restart
./scripts/runtime-check.sh --reset
npm run dev
```

---

**💡 Pro Tips:**
- Use `Ctrl+C` to gracefully stop services
- Always check `./scripts/runtime-check.sh` if something seems wrong
- Keep multiple terminals open for different services
- Use `npm run dev` for daily development
- Check logs when services fail to start

**📚 More Help:**
- [Runtime Guide](RUNTIME_GUIDE.md) - Detailed runtime documentation
- [Troubleshooting](TROUBLESHOOTING.md) - Common issues and solutions
- [Developer Setup](DEVELOPER_SETUP.md) - Initial setup guide
