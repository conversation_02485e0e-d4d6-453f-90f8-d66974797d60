# Emma Studio Developer Setup Guide

Welcome to Emma Studio! This comprehensive guide will help you set up your development environment and get started contributing to the project.

## 🚀 Quick Start

For a fully automated setup, use our setup scripts:

### Unix/macOS/Linux
```bash
chmod +x setup-dev-environment.sh
./setup-dev-environment.sh
```

### Windows
```cmd
setup-dev-environment.bat
```

## 📋 System Requirements

### Required Software
- **Node.js**: Version 18 or higher
- **Python**: Version 3.10, 3.11, or 3.12
- **Poetry**: Latest version for Python dependency management
- **Git**: Latest version

### Optional Software
- **Docker**: For running Qdrant vector database locally
- **VS Code**: Recommended IDE with extensions listed below

### Recommended VS Code Extensions
- Python
- Pylance
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- Prettier - Code formatter
- ESLint
- GitLens
- Thunder Client (for API testing)

## 🛠 Manual Setup Instructions

If you prefer to set up manually or the automated script doesn't work for your system:

### 1. Prerequisites Installation

#### macOS (using Homebrew)
```bash
# Install Homebrew if not already installed
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required software
brew install node python@3.11 git
brew install --cask docker

# Install Poetry
curl -sSL https://install.python-poetry.org | python3 -
export PATH="$HOME/.local/bin:$PATH"
```

#### Ubuntu/Debian
```bash
# Update package manager
sudo apt-get update

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install Python and pip
sudo apt-get install -y python3 python3-pip python3-venv

# Install Poetry
curl -sSL https://install.python-poetry.org | python3 -
export PATH="$HOME/.local/bin:$PATH"

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

#### Windows
1. **Node.js**: Download and install from [nodejs.org](https://nodejs.org/)
2. **Python**: Download and install from [python.org](https://python.org/)
3. **Poetry**: Follow instructions at [python-poetry.org](https://python-poetry.org/docs/#installation)
4. **Git**: Download and install from [git-scm.com](https://git-scm.com/)
5. **Docker Desktop**: Download from [docker.com](https://www.docker.com/products/docker-desktop)

### 2. Clone the Repository

```bash
git clone https://github.com/Agents4Work/emma-studio-.git
cd emma-studio-
```

### 3. Environment Configuration

#### Backend Environment
```bash
cp backend/.env.example backend/.env
```

#### Frontend Environment
```bash
cp .env.example .env
```

Edit both files with your API keys (see [API Keys Configuration](#api-keys-configuration) below).

### 4. Install Dependencies

#### Backend Dependencies
```bash
cd backend
poetry install
cd ..
```

#### Frontend Dependencies
```bash
cd client
npm install
cd ..
```

### 5. Database Setup

Emma Studio uses Supabase as its database. Follow these steps:

1. **Create a Supabase Project**:
   - Go to [supabase.com](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Configure Database Connection**:
   - Update `DATABASE_URL` in both `.env` files
   - Format: `postgresql://postgres:[password]@[host]:[port]/[database]`

3. **Run Database Schema**:
   - Open your Supabase SQL editor
   - Copy and run the contents of `supabase-schema.sql`

### 6. Optional: Docker Services

If you have Docker installed, you can run Qdrant locally:

```bash
docker run -d --name qdrant -p 6333:6333 qdrant/qdrant
```

## 🔑 API Keys Configuration

Emma Studio integrates with multiple AI and web services. You'll need to obtain API keys for:

### Required APIs
- **Gemini API**: Get from [Google AI Studio](https://makersuite.google.com/app/apikey)
- **Stability AI**: Get from [Stability AI Platform](https://platform.stability.ai/account/keys)
- **OpenAI**: Get from [OpenAI Platform](https://platform.openai.com/api-keys)

### Optional APIs
- **ElevenLabs**: For voice synthesis - [ElevenLabs](https://elevenlabs.io/)
- **Google Cloud**: For search and other services - [Google Cloud Console](https://console.cloud.google.com/)
- **Serper**: For SEO analysis - [Serper.dev](https://serper.dev/)
- **Polotno Studio**: For visual editor - [Polotno Cabinet](https://polotno.com/cabinet)
- **Browserless**: For web automation - [Browserless.io](https://www.browserless.io/)

### Environment Variables Template

Add these to both `.env` and `backend/.env`:

```env
# Core AI APIs
GEMINI_API_KEY=your_gemini_api_key_here
VITE_GEMINI_API_KEY=${GEMINI_API_KEY}
STABILITY_API_KEY=your_stability_api_key_here
VITE_STABILITY_API_KEY=${STABILITY_API_KEY}
OPENAI_API_KEY=your_openai_api_key_here
VITE_OPENAI_API_KEY=${OPENAI_API_KEY}

# Optional APIs
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here
VITE_ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_CSE_ID=your_google_cse_id_here
SERPER_API_KEY=your_serper_api_key_here
VITE_POLOTNO_API_KEY=your_polotno_api_key_here
BROWSERLESS_API_KEY=your_browserless_api_key_here

# Database
DATABASE_URL=postgresql://postgres:password@host:port/database

# Security
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# Development
DEBUG=true
LOG_LEVEL=INFO
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173
```

## 🚀 Running the Application

### Development Mode

#### Option 1: Start Both Services Together
```bash
npm run dev
```

#### Option 2: Start Services Separately

**Terminal 1 - Backend:**
```bash
cd backend
poetry run uvicorn app.main:app --reload --port 8000
```

**Terminal 2 - Frontend:**
```bash
cd client
npm run dev
```

### Production Mode

```bash
# Build frontend
npm run build:client

# Start backend in production mode
npm run start:backend
```

### Using Docker

```bash
# Development
docker-compose -f docker-compose.dev.yml up --build

# Production
docker-compose -f docker-compose.prod.yml up --build
```

## 📁 Project Structure

```
emma-studio/
├── backend/                 # Python FastAPI backend
│   ├── app/                # Main application code
│   │   ├── agents/         # AI agent implementations
│   │   ├── api/           # API routes
│   │   ├── core/          # Core configuration
│   │   ├── models/        # Database models
│   │   └── services/      # Business logic
│   ├── tests/             # Backend tests
│   ├── pyproject.toml     # Poetry configuration
│   └── .env.example       # Backend environment template
├── client/                 # React frontend
│   ├── src/
│   │   ├── components/    # React components
│   │   ├── pages/         # Page components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── utils/         # Utility functions
│   │   └── types/         # TypeScript type definitions
│   ├── package.json       # Frontend dependencies
│   └── vite.config.ts     # Vite configuration
├── docs/                   # Documentation
├── scripts/               # Utility scripts
├── docker-compose.yml     # Docker configuration
├── package.json           # Root package.json for scripts
└── .env.example           # Frontend environment template
```

## 🧪 Available Scripts

### Root Level Scripts
```bash
# Development
npm run dev                 # Start both backend and frontend
npm run dev:backend        # Start only backend
npm run dev:client         # Start only frontend

# Building
npm run build              # Build both backend and frontend
npm run build:client      # Build frontend only
npm run build:server      # Build backend only

# Production
npm run start              # Start both in production mode
npm run start:backend     # Start backend in production
npm run start:client      # Start frontend in production

# Code Quality
npm run format             # Format both backend and frontend code
npm run format:client     # Format frontend code only
npm run format:server     # Format backend code only
npm run lint               # Lint both backend and frontend
npm run lint:client       # Lint frontend only
npm run lint:server       # Lint backend only

# Database
npm run db:push            # Push database schema changes
```

### Backend Scripts (Poetry)
```bash
cd backend

# Development
poetry run uvicorn app.main:app --reload --port 8000

# Testing
poetry run pytest
poetry run pytest --cov=app tests/

# Code Quality
poetry run black .
poetry run isort .
poetry run flake8 .
poetry run mypy .

# Dependencies
poetry add package_name
poetry remove package_name
poetry update
```

### Frontend Scripts (npm)
```bash
cd client

# Development
npm run dev                # Start development server
npm run build             # Build for production
npm run preview           # Preview production build

# Code Quality
npm run lint              # Run ESLint
npm run format            # Run Prettier

# Dependencies
npm install package_name
npm uninstall package_name
npm update
```

## 🔧 Development Workflow

### 1. Daily Development
1. Pull latest changes: `git pull origin main`
2. Install any new dependencies: `npm install && cd backend && poetry install && cd ..`
3. Start development servers: `npm run dev`
4. Make your changes
5. Test your changes
6. Commit and push

### 2. Adding New Dependencies

#### Frontend Dependencies
```bash
cd client
npm install package-name
```

#### Backend Dependencies
```bash
cd backend
poetry add package-name
```

### 3. Code Style Guidelines

#### Frontend (TypeScript/React)
- Use TypeScript for all new code
- Follow React functional components with hooks
- Use Tailwind CSS for styling
- Components should be 50-150 lines following Single Responsibility Principle
- Organize components in dedicated folders with components/types/utils structure
- Use proper TypeScript interfaces
- Implement error boundaries
- Follow consistent naming conventions (no Spanish/English mixing)

#### Backend (Python)
- Follow PEP 8 style guidelines
- Use type hints for all functions
- Write docstrings for all public functions and classes
- Use async/await for I/O operations
- Follow SOLID principles and design patterns
- Implement proper error handling and logging

### 4. Testing

#### Frontend Testing
```bash
cd client
npm run test              # Run tests
npm run test:watch        # Run tests in watch mode
npm run test:coverage     # Run tests with coverage
```

#### Backend Testing
```bash
cd backend
poetry run pytest                    # Run all tests
poetry run pytest tests/test_api/    # Run specific test directory
poetry run pytest --cov=app tests/  # Run with coverage
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Find process using port
lsof -i :8000  # Backend port
lsof -i :5173  # Frontend port

# Kill process
kill -9 <PID>
```

#### Poetry Issues
```bash
# Clear Poetry cache
poetry cache clear pypi --all

# Reinstall dependencies
rm poetry.lock
poetry install
```

#### Node.js Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

#### Docker Issues
```bash
# Restart Docker
sudo systemctl restart docker  # Linux
# Or restart Docker Desktop on macOS/Windows

# Clean Docker
docker system prune -a
```

### Environment Issues

#### Missing API Keys
- Check that all required API keys are set in both `.env` files
- Verify API keys are valid and have proper permissions
- Check for typos in environment variable names

#### Database Connection Issues
- Verify Supabase project is active
- Check DATABASE_URL format
- Ensure database schema has been applied
- Verify network connectivity to Supabase

#### Import/Module Issues
- Ensure all dependencies are installed
- Check Python path configuration
- Verify virtual environment is activated (Poetry handles this automatically)

### Performance Issues

#### Slow Development Server
- Check if antivirus is scanning node_modules
- Exclude project directory from real-time scanning
- Use SSD storage for better performance

#### Memory Issues
- Increase Node.js memory limit: `export NODE_OPTIONS="--max-old-space-size=4096"`
- Close unnecessary applications
- Consider using Docker for resource isolation

## 📚 Additional Resources

### Documentation
- [Architecture Documentation](ARCHITECTURE.md)
- [Agent System Documentation](AGENT_ARCHITECTURE.md)
- [Contributing Guidelines](../CONTRIBUTING.md)
- [API Documentation](API_DOCUMENTATION.md)

### External Documentation
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://react.dev/)
- [Poetry Documentation](https://python-poetry.org/docs/)
- [Vite Documentation](https://vitejs.dev/)
- [Supabase Documentation](https://supabase.com/docs)

### Community
- [GitHub Issues](https://github.com/Agents4Work/emma-studio-/issues)
- [Discussions](https://github.com/Agents4Work/emma-studio-/discussions)

## 🆘 Getting Help

If you encounter issues not covered in this guide:

1. Check the [troubleshooting section](#troubleshooting) above
2. Search existing [GitHub issues](https://github.com/Agents4Work/emma-studio-/issues)
3. Create a new issue with:
   - Your operating system and version
   - Node.js and Python versions
   - Complete error messages
   - Steps to reproduce the issue
   - What you've already tried

## 🎉 You're Ready!

Congratulations! You now have a complete Emma Studio development environment. Start by exploring the codebase and checking out our [Contributing Guidelines](../CONTRIBUTING.md) to learn how to contribute to the project.

Happy coding! 🚀
