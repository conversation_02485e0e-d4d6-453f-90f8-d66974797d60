# Sistema de Análisis SEO Persistente

## Descripción General

El sistema de análisis SEO persistente permite realizar análisis exhaustivos de sitios web que pueden durar entre 30-60 minutos, manteniendo el progreso en base de datos y permitiendo a los usuarios cerrar la página y volver más tarde para ver los resultados.

## Características Principales

### 🔄 Análisis Persistente
- **Almacenamiento en Base de Datos**: Todos los análisis se guardan en la tabla `seo_analyses`
- **Progreso en Tiempo Real**: Seguimiento detallado del progreso con callbacks
- **Recuperación de Estado**: Los usuarios pueden cerrar la página y volver más tarde
- **Cancelación**: Posibilidad de cancelar análisis en progreso

### 📊 Dashboard de Análisis
- **Vista de Análisis Activos**: Monitoreo en tiempo real de análisis en progreso
- **Historial Completo**: Acceso a todos los análisis completados
- **Gestión de Errores**: Vista de análisis fallidos con detalles del error
- **Filtros y Búsqueda**: Organización eficiente de análisis

### ⚡ Dos Modos de Análisis
1. **Página Específica**: Análisis rápido (2-5 minutos) de una sola página
2. **Sitio Web Completo**: Análisis exhaustivo (30-60 minutos) de todo el sitio

## Arquitectura del Sistema

### Backend Components

#### 1. Modelo de Base de Datos (`SEOAnalysis`)
```python
class SEOAnalysis(Base):
    __tablename__ = "seo_analyses"
    
    # Identificación
    id = Column(Integer, primary_key=True)
    analysis_id = Column(String(64), unique=True)
    user_id = Column(String(64), nullable=True)
    
    # Configuración
    url = Column(String(512), nullable=False)
    mode = Column(String(32), nullable=False)  # "page" | "website"
    
    # Estado
    status = Column(String(32), nullable=False)  # "pending", "in_progress", "complete", "error", "cancelled"
    phase = Column(String(64), nullable=True)    # "discovery", "analysis", "recommendations", "complete"
    
    # Progreso
    current_page = Column(Integer, default=0)
    total_pages = Column(Integer, default=0)
    status_message = Column(Text, nullable=True)
    
    # Timing
    created_at = Column(DateTime, default=datetime.utcnow)
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    estimated_completion = Column(DateTime, nullable=True)
    processing_time = Column(Float, nullable=True)
    
    # Datos (JSON)
    progress_data = Column(Text)  # JSON string
    result_data = Column(Text)    # JSON string
    error_data = Column(Text)     # JSON string
```

#### 2. Servicio Persistente (`PersistentSEOService`)
- **Gestión de Análisis**: Crear, iniciar, cancelar análisis
- **Seguimiento de Progreso**: Callbacks para actualizar estado en tiempo real
- **Almacenamiento**: Persistencia de datos y resultados
- **Recuperación**: Obtener análisis por ID o usuario

#### 3. Endpoints API
```
POST /api/seo/analyze-website     # Iniciar análisis exhaustivo
GET  /api/seo/progress/{id}       # Obtener progreso de análisis
GET  /api/seo/analyses            # Listar análisis del usuario
DELETE /api/seo/analyses/{id}     # Cancelar análisis
```

### Frontend Components

#### 1. Hook de Análisis Persistente (`usePersistentSEO`)
```typescript
const persistentSEO = usePersistentSEO({
  url: "https://example.com",
  mode: "website",
  onComplete: (result) => console.log("Completed!", result),
  onError: (error) => console.error("Error:", error)
});

// Iniciar análisis
await persistentSEO.startAnalysis();

// Monitorear progreso
console.log(persistentSEO.progress);
console.log(persistentSEO.progressPercentage);

// Cancelar si es necesario
await persistentSEO.cancelAnalysis(analysisId);
```

#### 2. Dashboard de Análisis (`SEOAnalysisDashboard`)
- **Pestañas Organizadas**: Activos, Completados, Fallidos
- **Actualización Automática**: Polling cada 5 segundos
- **Acciones Rápidas**: Ver resultados, cancelar análisis
- **Información Detallada**: Progreso, timing, estadísticas

#### 3. Pantalla de Carga Mejorada (`SEOLoadingScreen`)
- **Indicadores Visuales**: Progreso, fase actual, tiempo transcurrido
- **Información Persistente**: Mensaje sobre capacidad de cerrar página
- **Botón de Cancelación**: Para análisis persistentes
- **URLs Procesadas**: Lista en tiempo real de páginas analizadas

## Flujo de Trabajo

### 1. Inicio de Análisis
```mermaid
sequenceDiagram
    participant U as Usuario
    participant F as Frontend
    participant B as Backend
    participant D as Database
    participant S as SEO Service

    U->>F: Solicita análisis exhaustivo
    F->>B: POST /api/seo/analyze-website
    B->>D: Crear registro SEOAnalysis
    B->>S: Iniciar análisis en background
    B->>F: Retorna analysis_id
    F->>U: Muestra progreso inicial
```

### 2. Seguimiento de Progreso
```mermaid
sequenceDiagram
    participant F as Frontend
    participant B as Backend
    participant D as Database
    participant S as SEO Service

    loop Cada 3 segundos
        F->>B: GET /api/seo/progress/{id}
        B->>D: Consultar estado actual
        D->>B: Datos de progreso
        B->>F: Estado actualizado
        F->>F: Actualizar UI
    end

    S->>D: Actualizar progreso (callback)
    Note over S,D: Análisis en background actualiza BD
```

### 3. Finalización
```mermaid
sequenceDiagram
    participant S as SEO Service
    participant D as Database
    participant F as Frontend
    participant U as Usuario

    S->>D: Guardar resultados finales
    S->>D: Marcar como "complete"
    F->>D: Detectar finalización (polling)
    F->>U: Mostrar resultados
    U->>F: Ver análisis completo
```

## Estados del Análisis

| Estado | Descripción | Acciones Disponibles |
|--------|-------------|---------------------|
| `pending` | Análisis creado, esperando inicio | Cancelar |
| `in_progress` | Análisis ejecutándose | Cancelar, Ver progreso |
| `complete` | Análisis finalizado exitosamente | Ver resultados |
| `error` | Análisis falló con error | Ver error, Reintentar |
| `cancelled` | Análisis cancelado por usuario | Reintentar |

## Fases del Análisis

| Fase | Descripción | Duración Estimada |
|------|-------------|------------------|
| `discovery` | Descubrimiento de páginas del sitio | 5-10% del tiempo total |
| `analysis` | Análisis individual de cada página | 80-90% del tiempo total |
| `recommendations` | Generación de recomendaciones con IA | 5-10% del tiempo total |
| `complete` | Análisis finalizado | - |

## Configuración y Despliegue

### 1. Base de Datos
```bash
# Ejecutar migración
cd backend
python scripts/init_seo_db.py
```

### 2. Variables de Entorno
```env
# Backend
DATABASE_URL=postgresql://user:pass@localhost/emma_db
GEMINI_API_KEY=your_gemini_key
JINA_API_KEY=your_jina_key

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### 3. Dependencias
```bash
# Backend
pip install sqlalchemy alembic asyncio

# Frontend
npm install @tanstack/react-query
```

## Monitoreo y Mantenimiento

### 1. Logs
- Todos los análisis se registran con nivel INFO
- Errores se registran con nivel ERROR
- Progreso se registra cada actualización

### 2. Limpieza de Datos
```sql
-- Limpiar análisis antiguos (más de 30 días)
DELETE FROM seo_analyses 
WHERE created_at < NOW() - INTERVAL '30 days' 
AND status IN ('complete', 'error', 'cancelled');
```

### 3. Métricas Importantes
- Tiempo promedio de análisis por modo
- Tasa de éxito/fallo
- Número de páginas analizadas por sitio
- Uso de recursos durante análisis largos

## Limitaciones y Consideraciones

### 1. Recursos
- Análisis exhaustivos consumen más CPU y memoria
- Límite recomendado: 50 páginas por sitio
- Timeout máximo: 60 minutos

### 2. Concurrencia
- Máximo 5 análisis simultáneos por usuario
- Cola de análisis para gestionar carga

### 3. Almacenamiento
- Resultados JSON pueden ser grandes (1-10MB por análisis)
- Considerar compresión para análisis antiguos

## Próximas Mejoras

1. **Notificaciones**: Email/SMS cuando análisis completa
2. **Comparaciones**: Comparar análisis del mismo sitio en el tiempo
3. **Exportación**: PDF/Excel de resultados
4. **Análisis Programados**: Análisis automáticos periódicos
5. **API Webhooks**: Notificaciones a sistemas externos
