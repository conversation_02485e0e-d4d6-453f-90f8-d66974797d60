#!/bin/bash

# Emma Studio Setup Verification Script
# This script verifies that the development environment is properly configured

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if port is available
is_port_available() {
    ! lsof -i :$1 >/dev/null 2>&1
}

# Verify system requirements
verify_system_requirements() {
    print_header "Verifying System Requirements"
    
    local errors=0
    
    # Check Node.js
    if command_exists node; then
        local node_version=$(node --version)
        print_success "Node.js $node_version is installed"
    else
        print_error "Node.js is not installed"
        errors=$((errors + 1))
    fi
    
    # Check npm
    if command_exists npm; then
        local npm_version=$(npm --version)
        print_success "npm $npm_version is installed"
    else
        print_error "npm is not installed"
        errors=$((errors + 1))
    fi
    
    # Check Python
    if command_exists python3; then
        local python_version=$(python3 --version)
        print_success "$python_version is installed"
    else
        print_error "Python 3 is not installed"
        errors=$((errors + 1))
    fi
    
    # Check Poetry
    if command_exists poetry; then
        local poetry_version=$(poetry --version)
        print_success "$poetry_version is installed"
    else
        print_error "Poetry is not installed"
        errors=$((errors + 1))
    fi
    
    # Check Git
    if command_exists git; then
        local git_version=$(git --version)
        print_success "$git_version is installed"
    else
        print_error "Git is not installed"
        errors=$((errors + 1))
    fi
    
    # Check Docker (optional)
    if command_exists docker; then
        if docker info >/dev/null 2>&1; then
            print_success "Docker is installed and running"
        else
            print_warning "Docker is installed but not running"
        fi
    else
        print_warning "Docker is not installed (optional)"
    fi
    
    return $errors
}

# Verify project structure
verify_project_structure() {
    print_header "Verifying Project Structure"
    
    local errors=0
    
    # Check root files
    local required_files=("package.json" "pyproject.toml" ".env.example" "README.md")
    for file in "${required_files[@]}"; do
        if [ -f "$file" ]; then
            print_success "$file exists"
        else
            print_error "$file is missing"
            errors=$((errors + 1))
        fi
    done
    
    # Check directories
    local required_dirs=("backend" "client" "docs")
    for dir in "${required_dirs[@]}"; do
        if [ -d "$dir" ]; then
            print_success "$dir/ directory exists"
        else
            print_error "$dir/ directory is missing"
            errors=$((errors + 1))
        fi
    done
    
    # Check backend structure
    if [ -d "backend" ]; then
        local backend_files=("pyproject.toml" ".env.example" "main.py")
        for file in "${backend_files[@]}"; do
            if [ -f "backend/$file" ]; then
                print_success "backend/$file exists"
            else
                print_error "backend/$file is missing"
                errors=$((errors + 1))
            fi
        done
    fi
    
    # Check client structure
    if [ -d "client" ]; then
        local client_files=("package.json" "vite.config.ts" "tsconfig.json")
        for file in "${client_files[@]}"; do
            if [ -f "client/$file" ]; then
                print_success "client/$file exists"
            else
                print_error "client/$file is missing"
                errors=$((errors + 1))
            fi
        done
    fi
    
    return $errors
}

# Verify dependencies
verify_dependencies() {
    print_header "Verifying Dependencies"
    
    local errors=0
    
    # Check backend dependencies
    if [ -d "backend" ]; then
        cd backend
        if poetry check >/dev/null 2>&1; then
            print_success "Backend Poetry configuration is valid"
            
            # Check if dependencies are installed
            if [ -d ".venv" ] || poetry env info >/dev/null 2>&1; then
                print_success "Backend virtual environment exists"
            else
                print_warning "Backend dependencies may not be installed"
            fi
        else
            print_error "Backend Poetry configuration has issues"
            errors=$((errors + 1))
        fi
        cd ..
    fi
    
    # Check frontend dependencies
    if [ -d "client" ]; then
        if [ -d "client/node_modules" ]; then
            print_success "Frontend dependencies are installed"
        else
            print_warning "Frontend dependencies may not be installed"
        fi
    fi
    
    return $errors
}

# Verify environment configuration
verify_environment() {
    print_header "Verifying Environment Configuration"
    
    local warnings=0
    
    # Check environment files
    if [ -f ".env" ]; then
        print_success "Root .env file exists"
    else
        print_warning "Root .env file is missing"
        warnings=$((warnings + 1))
    fi
    
    if [ -f "backend/.env" ]; then
        print_success "Backend .env file exists"
    else
        print_warning "Backend .env file is missing"
        warnings=$((warnings + 1))
    fi
    
    # Check for common API keys (without revealing values)
    if [ -f ".env" ]; then
        local api_keys=("GEMINI_API_KEY" "STABILITY_API_KEY" "OPENAI_API_KEY")
        for key in "${api_keys[@]}"; do
            if grep -q "^$key=" .env && ! grep -q "^$key=your_" .env; then
                print_success "$key is configured"
            else
                print_warning "$key is not configured or using placeholder value"
                warnings=$((warnings + 1))
            fi
        done
    fi
    
    return $warnings
}

# Verify ports
verify_ports() {
    print_header "Verifying Port Availability"
    
    local warnings=0
    
    # Check backend port
    if is_port_available 8000; then
        print_success "Backend port 8000 is available"
    else
        print_warning "Backend port 8000 is in use"
        warnings=$((warnings + 1))
    fi
    
    # Check frontend port
    if is_port_available 5173; then
        print_success "Frontend port 5173 is available"
    else
        print_warning "Frontend port 5173 is in use"
        warnings=$((warnings + 1))
    fi
    
    # Check Qdrant port
    if is_port_available 6333; then
        print_success "Qdrant port 6333 is available"
    else
        print_warning "Qdrant port 6333 is in use (this is OK if Qdrant is running)"
    fi
    
    return $warnings
}

# Test basic functionality
test_basic_functionality() {
    print_header "Testing Basic Functionality"
    
    local errors=0
    
    # Test backend import
    if [ -d "backend" ]; then
        cd backend
        if poetry run python -c "import app.main" >/dev/null 2>&1; then
            print_success "Backend imports work correctly"
        else
            print_error "Backend has import issues"
            errors=$((errors + 1))
        fi
        cd ..
    fi
    
    # Test frontend build (quick check)
    if [ -d "client" ]; then
        cd client
        if npm run build --dry-run >/dev/null 2>&1; then
            print_success "Frontend build configuration is valid"
        else
            print_warning "Frontend build configuration may have issues"
        fi
        cd ..
    fi
    
    return $errors
}

# Main verification function
main() {
    print_header "Emma Studio Setup Verification"
    echo "This script will verify your development environment setup."
    echo ""
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "backend" ] || [ ! -d "client" ]; then
        print_error "This doesn't appear to be the Emma Studio project root directory."
        print_error "Please run this script from the project root where package.json exists."
        exit 1
    fi
    
    local total_errors=0
    local total_warnings=0
    
    # Run all verification steps
    verify_system_requirements
    total_errors=$((total_errors + $?))
    
    verify_project_structure
    total_errors=$((total_errors + $?))
    
    verify_dependencies
    total_errors=$((total_errors + $?))
    
    verify_environment
    total_warnings=$((total_warnings + $?))
    
    verify_ports
    total_warnings=$((total_warnings + $?))
    
    test_basic_functionality
    total_errors=$((total_errors + $?))
    
    # Summary
    print_header "Verification Summary"
    
    if [ $total_errors -eq 0 ]; then
        print_success "✅ All critical checks passed!"
    else
        print_error "❌ $total_errors critical issues found"
    fi
    
    if [ $total_warnings -gt 0 ]; then
        print_warning "⚠️  $total_warnings warnings (non-critical)"
    fi
    
    echo ""
    if [ $total_errors -eq 0 ]; then
        echo -e "${GREEN}🎉 Your Emma Studio development environment is ready!${NC}"
        echo ""
        echo "Next steps:"
        echo "1. Configure your API keys in .env files if not already done"
        echo "2. Start the development servers with: npm run dev"
        echo "3. Open http://localhost:5173 in your browser"
    else
        echo -e "${RED}❌ Please fix the critical issues above before proceeding.${NC}"
        echo ""
        echo "For help, see docs/DEVELOPER_SETUP.md or run the setup script:"
        echo "./setup-dev-environment.sh"
    fi
    
    exit $total_errors
}

# Run main function
main "$@"
