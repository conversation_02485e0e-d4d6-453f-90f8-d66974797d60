#!/bin/bash

# Emma Studio Runtime Health Check Script
# This script checks the health of all running services

set -e

# Color codes
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m'

# Configuration
BACKEND_PORT=8000
FRONTEND_PORT=5173
QDRANT_PORT=6333

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Check if port is in use
is_port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Check if service responds to HTTP
check_http_service() {
    local url=$1
    local service_name=$2
    
    if curl -s --max-time 5 "$url" >/dev/null 2>&1; then
        print_success "$service_name is responding at $url"
        return 0
    else
        print_error "$service_name is not responding at $url"
        return 1
    fi
}

# Check service health
check_service_health() {
    print_header "Service Health Check"
    
    local errors=0
    
    # Check Backend
    print_status "Checking Backend (port $BACKEND_PORT)..."
    if is_port_in_use $BACKEND_PORT; then
        if check_http_service "http://localhost:$BACKEND_PORT/health" "Backend API"; then
            # Check API docs
            if curl -s --max-time 5 "http://localhost:$BACKEND_PORT/docs" >/dev/null 2>&1; then
                print_success "Backend API documentation is accessible"
            else
                print_warning "Backend API documentation may not be accessible"
            fi
        else
            errors=$((errors + 1))
        fi
    else
        print_error "Backend is not running on port $BACKEND_PORT"
        errors=$((errors + 1))
    fi
    
    # Check Frontend
    print_status "Checking Frontend (port $FRONTEND_PORT)..."
    if is_port_in_use $FRONTEND_PORT; then
        if check_http_service "http://localhost:$FRONTEND_PORT" "Frontend"; then
            print_success "Frontend is accessible"
        else
            errors=$((errors + 1))
        fi
    else
        print_error "Frontend is not running on port $FRONTEND_PORT"
        errors=$((errors + 1))
    fi
    
    # Check Qdrant (optional)
    print_status "Checking Qdrant (port $QDRANT_PORT)..."
    if is_port_in_use $QDRANT_PORT; then
        if check_http_service "http://localhost:$QDRANT_PORT/health" "Qdrant"; then
            # Check collections
            if curl -s --max-time 5 "http://localhost:$QDRANT_PORT/collections" >/dev/null 2>&1; then
                print_success "Qdrant collections endpoint is accessible"
            else
                print_warning "Qdrant collections endpoint may not be accessible"
            fi
        else
            print_warning "Qdrant is running but not responding properly"
        fi
    else
        print_warning "Qdrant is not running (optional service)"
    fi
    
    return $errors
}

# Check running processes
check_processes() {
    print_header "Process Information"
    
    # Backend processes
    print_status "Backend processes:"
    if pgrep -f "uvicorn.*app.main:app" >/dev/null; then
        ps aux | grep -E "uvicorn.*app.main:app" | grep -v grep | while read line; do
            echo "  ✅ $line"
        done
    else
        print_warning "No backend processes found"
    fi
    
    # Frontend processes
    print_status "Frontend processes:"
    if pgrep -f "vite.*dev" >/dev/null; then
        ps aux | grep -E "vite.*dev" | grep -v grep | while read line; do
            echo "  ✅ $line"
        done
    else
        print_warning "No frontend processes found"
    fi
    
    # Docker processes
    print_status "Docker containers:"
    if docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(qdrant|emma)" 2>/dev/null; then
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep -E "(qdrant|emma)" | while read line; do
            echo "  ✅ $line"
        done
    else
        print_warning "No relevant Docker containers found"
    fi
}

# Check port usage
check_ports() {
    print_header "Port Usage"
    
    local ports=($BACKEND_PORT $FRONTEND_PORT $QDRANT_PORT)
    local services=("Backend" "Frontend" "Qdrant")
    
    for i in "${!ports[@]}"; do
        local port=${ports[$i]}
        local service=${services[$i]}
        
        if is_port_in_use $port; then
            local process=$(lsof -ti:$port | head -1)
            local process_name=$(ps -p $process -o comm= 2>/dev/null || echo "unknown")
            print_success "$service port $port is in use by process $process ($process_name)"
        else
            if [ "$service" = "Qdrant" ]; then
                print_warning "$service port $port is not in use (optional)"
            else
                print_error "$service port $port is not in use"
            fi
        fi
    done
}

# Check environment
check_environment() {
    print_header "Environment Check"
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "backend" ] || [ ! -d "client" ]; then
        print_error "Not in Emma Studio project directory"
        return 1
    fi
    
    print_success "In Emma Studio project directory"
    
    # Check environment files
    if [ -f ".env" ]; then
        print_success "Root .env file exists"
    else
        print_warning "Root .env file missing"
    fi
    
    if [ -f "backend/.env" ]; then
        print_success "Backend .env file exists"
    else
        print_warning "Backend .env file missing"
    fi
    
    # Check key environment variables (without revealing values)
    local env_vars=("GEMINI_API_KEY" "STABILITY_API_KEY" "OPENAI_API_KEY")
    for var in "${env_vars[@]}"; do
        if [ -f ".env" ] && grep -q "^$var=" .env && ! grep -q "^$var=your_" .env; then
            print_success "$var is configured"
        else
            print_warning "$var may not be configured"
        fi
    done
}

# Performance check
check_performance() {
    print_header "Performance Check"
    
    # Memory usage
    print_status "Memory usage for Emma Studio processes:"
    ps aux --sort=-%mem | grep -E "(uvicorn|vite|node.*dev|qdrant)" | grep -v grep | head -10 | while read line; do
        echo "  $line"
    done
    
    # Response times
    print_status "API response times:"
    
    if is_port_in_use $BACKEND_PORT; then
        local response_time=$(curl -w "%{time_total}" -o /dev/null -s http://localhost:$BACKEND_PORT/health 2>/dev/null || echo "failed")
        if [ "$response_time" != "failed" ]; then
            print_success "Backend health endpoint: ${response_time}s"
        else
            print_error "Backend health endpoint: failed"
        fi
    fi
    
    if is_port_in_use $FRONTEND_PORT; then
        local response_time=$(curl -w "%{time_total}" -o /dev/null -s http://localhost:$FRONTEND_PORT 2>/dev/null || echo "failed")
        if [ "$response_time" != "failed" ]; then
            print_success "Frontend: ${response_time}s"
        else
            print_error "Frontend: failed"
        fi
    fi
}

# Quick fix suggestions
suggest_fixes() {
    print_header "Quick Fix Suggestions"
    
    echo "If services are not running:"
    echo "  🚀 Start all services: npm run dev"
    echo "  🔧 Start backend only: npm run dev:backend"
    echo "  🎨 Start frontend only: npm run dev:client"
    echo "  🐳 Start Qdrant: docker run -d --name qdrant -p 6333:6333 qdrant/qdrant"
    echo ""
    echo "If ports are in use:"
    echo "  🔍 Find process: lsof -i :PORT"
    echo "  ❌ Kill process: kill -9 PID"
    echo "  🔄 Use different port: --port NEWPORT"
    echo ""
    echo "If services are unresponsive:"
    echo "  🔄 Restart services: Ctrl+C then npm run dev"
    echo "  🧹 Clear caches: rm -rf client/node_modules/.vite backend/.venv"
    echo "  🆘 Emergency reset: ./scripts/runtime-check.sh --reset"
}

# Emergency reset function
emergency_reset() {
    print_header "Emergency Reset"
    
    print_status "Stopping all Emma Studio processes..."
    pkill -f "uvicorn.*app.main:app" || true
    pkill -f "vite.*dev" || true
    
    print_status "Stopping Docker containers..."
    docker stop qdrant 2>/dev/null || true
    
    print_status "Clearing caches..."
    [ -d "client/node_modules/.vite" ] && rm -rf client/node_modules/.vite
    [ -d "backend/__pycache__" ] && find backend -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    print_status "Restarting services..."
    sleep 2
    
    # Start Qdrant
    if command -v docker >/dev/null 2>&1; then
        docker run -d --name qdrant -p 6333:6333 qdrant/qdrant 2>/dev/null || true
    fi
    
    print_success "Emergency reset complete. Run 'npm run dev' to start services."
}

# Main function
main() {
    case "${1:-}" in
        --reset)
            emergency_reset
            exit 0
            ;;
        --help|-h)
            echo "Emma Studio Runtime Health Check"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --reset     Emergency reset of all services"
            echo "  --help, -h  Show this help message"
            echo ""
            echo "This script checks:"
            echo "  - Service health and availability"
            echo "  - Running processes"
            echo "  - Port usage"
            echo "  - Environment configuration"
            echo "  - Performance metrics"
            exit 0
            ;;
    esac
    
    print_header "Emma Studio Runtime Health Check"
    echo "Checking the health of all Emma Studio services..."
    echo ""
    
    local total_errors=0
    
    check_environment
    check_service_health
    total_errors=$((total_errors + $?))
    check_processes
    check_ports
    check_performance
    
    echo ""
    if [ $total_errors -eq 0 ]; then
        print_success "🎉 All critical services are healthy!"
    else
        print_error "⚠️  $total_errors issues found"
        suggest_fixes
    fi
    
    echo ""
    echo "For more help, see docs/RUNTIME_GUIDE.md"
}

# Run main function
main "$@"
