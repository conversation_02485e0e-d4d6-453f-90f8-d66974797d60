# Emma Studio Scripts

This directory contains utility scripts for Emma Studio development and maintenance.

## 🚀 Setup Scripts

### `../setup-dev-environment.sh`
Comprehensive development environment setup script for Unix/macOS/Linux systems.

**Usage:**
```bash
./setup-dev-environment.sh
```

**Features:**
- Checks system requirements
- Installs missing prerequisites (with `--install-prereqs`)
- Sets up environment files
- Installs backend and frontend dependencies
- Configures Docker services
- Verifies installation

### `../setup-dev-environment.bat`
Windows equivalent of the setup script.

**Usage:**
```cmd
setup-dev-environment.bat
```

## 🔍 Verification Scripts

### `verify-setup.sh`
Verifies that the development environment is properly configured.

**Usage:**
```bash
./scripts/verify-setup.sh
```

**Checks:**
- System requirements
- Project structure
- Dependencies installation
- Environment configuration
- Port availability
- Basic functionality

### `runtime-check.sh`
Checks the health of running services during development.

**Usage:**
```bash
./scripts/runtime-check.sh          # Health check
./scripts/runtime-check.sh --reset  # Emergency reset
```

**Checks:**
- Service health and availability
- Running processes
- Port usage
- Environment configuration
- Performance metrics
- Provides fix suggestions

## 📋 Script Options

### Setup Script Options
```bash
# Run normal setup
./setup-dev-environment.sh

# Attempt to install prerequisites automatically
./setup-dev-environment.sh --install-prereqs

# Show help
./setup-dev-environment.sh --help
```

## 🛠 Adding New Scripts

When adding new scripts to this directory:

1. **Make them executable:**
   ```bash
   chmod +x scripts/your-script.sh
   ```

2. **Add proper shebang:**
   ```bash
   #!/bin/bash
   ```

3. **Include error handling:**
   ```bash
   set -e  # Exit on error
   ```

4. **Add help documentation:**
   ```bash
   # Add --help option
   # Document in this README
   ```

5. **Use consistent styling:**
   - Color codes for output
   - Proper error messages
   - Status indicators

## 🎨 Output Styling

Scripts use consistent color coding:
- 🔵 **Blue**: Informational messages
- 🟢 **Green**: Success messages
- 🟡 **Yellow**: Warning messages
- 🔴 **Red**: Error messages
- 🟣 **Purple**: Section headers

## 🔧 Maintenance Scripts

### Future Scripts (Planned)
- `update-dependencies.sh` - Update all project dependencies
- `clean-environment.sh` - Clean development environment
- `backup-config.sh` - Backup configuration files
- `deploy-staging.sh` - Deploy to staging environment
- `run-tests.sh` - Run all test suites

## 📚 Related Documentation

- [Developer Setup Guide](../docs/DEVELOPER_SETUP.md)
- [Troubleshooting Guide](../docs/TROUBLESHOOTING.md)
- [API Keys Setup](../docs/API_KEYS_SETUP.md)

## 🆘 Getting Help

If you encounter issues with any scripts:

1. Check the [Troubleshooting Guide](../docs/TROUBLESHOOTING.md)
2. Run with verbose output (if supported)
3. Check script permissions
4. Verify you're in the correct directory
5. Create an issue with error details

## 🤝 Contributing

When contributing new scripts:

1. Follow existing patterns and styling
2. Add comprehensive error handling
3. Include help documentation
4. Test on multiple platforms (if applicable)
5. Update this README
6. Add to the main documentation if needed
