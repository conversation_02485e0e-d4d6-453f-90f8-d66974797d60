@echo off
REM Emma Studio Development Environment Setup Script for Windows
REM This script automates the complete setup process for new developers

setlocal enabledelayedexpansion

REM Configuration
set PROJECT_NAME=Emma Studio
set REQUIRED_NODE_VERSION=18
set REQUIRED_PYTHON_VERSION=3.10
set BACKEND_PORT=8000
set FRONTEND_PORT=5173

REM Color codes (limited in batch)
set RED=[91m
set GREEN=[92m
set YELLOW=[93m
set BLUE=[94m
set PURPLE=[95m
set CYAN=[96m
set NC=[0m

echo %PURPLE%================================%NC%
echo %PURPLE%Emma Studio Development Setup%NC%
echo %PURPLE%================================%NC%
echo.
echo %CYAN%Welcome to %PROJECT_NAME% development setup!%NC%
echo This script will set up your complete development environment.
echo.

REM Check if we're in the right directory
if not exist "package.json" (
    echo %RED%[ERROR]%NC% This doesn't appear to be the Emma Studio project root directory.
    echo %RED%[ERROR]%NC% Please run this script from the project root where package.json exists.
    pause
    exit /b 1
)

if not exist "backend" (
    echo %RED%[ERROR]%NC% Backend directory not found.
    pause
    exit /b 1
)

if not exist "client" (
    echo %RED%[ERROR]%NC% Client directory not found.
    pause
    exit /b 1
)

REM Ask for confirmation
set /p REPLY="Do you want to continue with the setup? (y/N): "
if /i not "%REPLY%"=="y" (
    echo %BLUE%[INFO]%NC% Setup cancelled by user.
    pause
    exit /b 0
)

echo.
echo %PURPLE%================================%NC%
echo %PURPLE%Checking System Requirements%NC%
echo %PURPLE%================================%NC%

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Node.js is not installed
    echo Please install Node.js 18+ from https://nodejs.org/
    set MISSING_PREREQS=1
) else (
    for /f "tokens=1 delims=." %%a in ('node --version') do (
        set NODE_MAJOR=%%a
        set NODE_MAJOR=!NODE_MAJOR:v=!
    )
    if !NODE_MAJOR! geq %REQUIRED_NODE_VERSION% (
        echo %GREEN%[SUCCESS]%NC% Node.js is installed
    ) else (
        echo %RED%[ERROR]%NC% Node.js version is too old. Required: %REQUIRED_NODE_VERSION%+
        set MISSING_PREREQS=1
    )
)

REM Check npm
npm --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% npm is not installed
    set MISSING_PREREQS=1
) else (
    echo %GREEN%[SUCCESS]%NC% npm is installed
)

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Python is not installed
    echo Please install Python 3.10+ from https://python.org/
    set MISSING_PREREQS=1
) else (
    echo %GREEN%[SUCCESS]%NC% Python is installed
)

REM Check Poetry
poetry --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Poetry is not installed
    echo Please install Poetry from https://python-poetry.org/docs/#installation
    set MISSING_PREREQS=1
) else (
    echo %GREEN%[SUCCESS]%NC% Poetry is installed
)

REM Check Docker
docker --version >nul 2>&1
if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Docker is not installed (optional for Qdrant)
    echo You can install Docker Desktop from https://www.docker.com/products/docker-desktop
) else (
    echo %GREEN%[SUCCESS]%NC% Docker is installed
)

REM Check Git
git --version >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Git is not installed
    echo Please install Git from https://git-scm.com/
    set MISSING_PREREQS=1
) else (
    echo %GREEN%[SUCCESS]%NC% Git is installed
)

if defined MISSING_PREREQS (
    echo.
    echo %RED%[ERROR]%NC% Some prerequisites are missing. Please install them and run this script again.
    echo.
    echo Required software:
    echo - Node.js 18+ from https://nodejs.org/
    echo - Python 3.10+ from https://python.org/
    echo - Poetry from https://python-poetry.org/docs/#installation
    echo - Git from https://git-scm.com/
    echo.
    echo Optional:
    echo - Docker Desktop from https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
)

echo.
echo %PURPLE%================================%NC%
echo %PURPLE%Setting Up Environment%NC%
echo %PURPLE%================================%NC%

REM Setup environment files
if not exist "backend\.env" (
    echo %BLUE%[INFO]%NC% Creating backend\.env from template...
    copy "backend\.env.example" "backend\.env" >nul
    echo %YELLOW%[WARNING]%NC% Please edit backend\.env with your API keys
) else (
    echo %GREEN%[SUCCESS]%NC% Backend .env file already exists
)

if not exist ".env" (
    echo %BLUE%[INFO]%NC% Creating .env from template...
    copy ".env.example" ".env" >nul
    echo %YELLOW%[WARNING]%NC% Please edit .env with your API keys
) else (
    echo %GREEN%[SUCCESS]%NC% Root .env file already exists
)

echo.
echo %PURPLE%================================%NC%
echo %PURPLE%Installing Backend Dependencies%NC%
echo %PURPLE%================================%NC%

cd backend
echo %BLUE%[INFO]%NC% Installing Python dependencies with Poetry...
poetry install
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install backend dependencies
    cd ..
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% Backend dependencies installed successfully
cd ..

echo.
echo %PURPLE%================================%NC%
echo %PURPLE%Installing Frontend Dependencies%NC%
echo %PURPLE%================================%NC%

cd client
echo %BLUE%[INFO]%NC% Installing Node.js dependencies...
npm install
if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to install frontend dependencies
    cd ..
    pause
    exit /b 1
)
echo %GREEN%[SUCCESS]%NC% Frontend dependencies installed successfully
cd ..

echo.
echo %PURPLE%================================%NC%
echo %PURPLE%Setting Up Database%NC%
echo %PURPLE%================================%NC%

echo %BLUE%[INFO]%NC% Database setup requires Supabase configuration
echo Please follow these steps:
echo 1. Create a Supabase project at https://supabase.com
echo 2. Copy your project URL and anon key
echo 3. Update DATABASE_URL in your .env files
echo 4. Run the SQL schema from supabase-schema.sql in your Supabase SQL editor

if exist "supabase-schema.sql" (
    echo %GREEN%[SUCCESS]%NC% Database schema file found: supabase-schema.sql
) else (
    echo %YELLOW%[WARNING]%NC% Database schema file not found
)

echo.
echo %PURPLE%================================%NC%
echo %PURPLE%Setup Complete!%NC%
echo %PURPLE%================================%NC%

echo.
echo %GREEN%🎉 Emma Studio development environment setup is complete!%NC%
echo.
echo Next steps:
echo 1. Configure your API keys in .env and backend\.env files
echo 2. Set up your Supabase database using supabase-schema.sql
echo 3. Start the development servers:
echo.
echo    %CYAN%# Start backend (in one terminal):%NC%
echo    cd backend ^&^& poetry run uvicorn app.main:app --reload --port 8000
echo.
echo    %CYAN%# Start frontend (in another terminal):%NC%
echo    cd client ^&^& npm run dev
echo.
echo    %CYAN%# Or start both with:%NC%
echo    npm run dev
echo.
echo 4. Open your browser to http://localhost:5173
echo.
echo For detailed documentation, see docs\DEVELOPER_SETUP.md
echo.
echo %YELLOW%Important:%NC% Make sure to configure all required API keys before starting the application.
echo.

pause
