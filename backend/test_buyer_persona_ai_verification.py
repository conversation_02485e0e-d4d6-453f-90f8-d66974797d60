#!/usr/bin/env python3
"""
Test script to verify if the buyer persona service is generating real AI-powered data
or just returning simulated/mock data.

This script will:
1. Check if Gemini AI is properly configured
2. Test actual AI generation vs mock data
3. Verify the quality and authenticity of generated personas
4. Ensure no fallback to simulated data when Gemini is available
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add the backend app to the Python path
backend_path = Path(__file__).parent
sys.path.insert(0, str(backend_path))

from app.core.config import settings
from app.services.buyer_persona_service import buyer_persona_service
from app.schemas.buyer_persona import BuyerPersonaRequest, SmartFormData


async def test_gemini_configuration():
    """Test if Gemini AI is properly configured."""
    print("🔍 Testing Gemini AI Configuration...")
    
    # Check if API key is set
    gemini_key = settings.GEMINI_API_KEY
    if not gemini_key:
        print("❌ GEMINI_API_KEY not found in environment variables")
        return False
    
    # Check if service has Gemini model initialized
    if not buyer_persona_service.gemini_model:
        print("❌ Gemini model not initialized in buyer_persona_service")
        return False
    
    print(f"✅ Gemini API Key configured: {gemini_key[:10]}...")
    print(f"✅ Gemini model initialized: {buyer_persona_service.model_name}")
    return True


async def test_ai_vs_mock_generation():
    """Test actual AI generation vs mock data to verify authenticity."""
    print("\n🧪 Testing AI vs Mock Generation...")
    
    # Create a test request
    test_request = BuyerPersonaRequest(
        product_description="Una plataforma de marketing digital con IA para empresas pequeñas y medianas",
        num_personas=2,
        industry="Tecnología",
        business_goals="Aumentar ventas y mejorar eficiencia de marketing",
        competitors="HubSpot, Mailchimp, Salesforce",
        request_timestamp=1234567890
    )
    
    try:
        # Test with AI (if available)
        if buyer_persona_service.gemini_model:
            print("🤖 Testing with Gemini AI...")
            ai_response = await buyer_persona_service.generate_buyer_personas(test_request)
            
            if ai_response.status == "success" and ai_response.buyer_personas:
                print(f"✅ AI generated {len(ai_response.buyer_personas)} personas successfully")
                print(f"   Generation time: {ai_response.generation_time_seconds:.2f}s")
                print(f"   AI model used: {ai_response.ai_model_used}")
                
                # Check for AI-specific characteristics
                persona = ai_response.buyer_personas[0]
                print(f"   Sample persona: {persona.name}")
                print(f"   Background: {persona.personal_background[:100]}...")
                
                # Verify it's not mock data by checking against known mock names
                mock_names = ["Ana García Martínez", "Carlos Rodríguez López", "María Elena Fernández"]
                is_mock = persona.name in mock_names
                
                if is_mock:
                    print("⚠️  WARNING: Generated persona matches mock data template!")
                    return False
                else:
                    print("✅ Generated persona appears to be AI-generated (unique name)")
                    return True
            else:
                print(f"❌ AI generation failed: {ai_response.error_message}")
                return False
        else:
            print("❌ Gemini AI not available for testing")
            return False
            
    except Exception as e:
        print(f"❌ Error during AI generation test: {e}")
        return False


async def test_prompt_quality():
    """Test the quality and comprehensiveness of the Gemini prompt."""
    print("\n📝 Testing Prompt Quality...")
    
    # Create a comprehensive test request with smart form data
    smart_form = SmartFormData(
        product_name="Emma AI Marketing Platform",
        product_type="software",
        industry="Marketing Technology",
        price_range="high",
        unique_value="AI-powered marketing automation with real-time analytics",
        target_audience="Marketing directors and CMOs in mid-size companies",
        audience_knowledge="clear",
        existing_audience="Digital marketing professionals",
        business_sizes=["Mediana", "Grande"],
        main_problem="Inefficient marketing campaigns and poor ROI tracking",
        problem_urgency="important",
        decision_maker="Marketing Director",
        geographic_scope="international",
        target_country="España",
        target_regions=["Madrid", "Barcelona", "Valencia"],
        sales_channels=["Online", "Direct Sales", "Partners"]
    )
    
    test_request = BuyerPersonaRequest(
        smart_form_data=smart_form,
        num_personas=3,
        business_goals="Increase market share and improve customer acquisition",
        competitors="HubSpot, Marketo, Pardot"
    )
    
    # Build the product description
    product_description = buyer_persona_service._build_product_description(test_request)
    print(f"✅ Product description built ({len(product_description)} characters)")
    
    # Create the prompt
    prompt = buyer_persona_service._create_enhanced_persona_prompt(product_description, test_request)
    print(f"✅ Prompt created ({len(prompt)} characters)")
    
    # Check prompt quality indicators
    quality_indicators = [
        "COMPLETAMENTE ÚNICOS Y DIVERSOS",
        "temperatura",
        "JSON",
        "marketing",
        "buying_process",
        "avatar_description",
        "insights"
    ]
    
    missing_indicators = []
    for indicator in quality_indicators:
        if indicator.lower() not in prompt.lower():
            missing_indicators.append(indicator)
    
    if missing_indicators:
        print(f"⚠️  Missing quality indicators: {missing_indicators}")
    else:
        print("✅ Prompt contains all quality indicators")
    
    # Check for diversity instructions
    if "COMPLETAMENTE DIFERENTES" in prompt and "ÚNICOS" in prompt:
        print("✅ Prompt includes strong diversity instructions")
    else:
        print("⚠️  Prompt may lack sufficient diversity instructions")
    
    return len(missing_indicators) == 0


async def test_response_parsing():
    """Test the response parsing to ensure it handles real AI responses correctly."""
    print("\n🔧 Testing Response Parsing...")
    
    # Create a mock AI response that simulates what Gemini would return
    mock_ai_response = """{
  "personas": [
    {
      "name": "Isabella Moreno García",
      "age": 38,
      "gender": "Femenino",
      "location": "Madrid, España",
      "education": "MBA en Marketing Digital",
      "income_level": "Alto",
      "marital_status": "Casada",
      "job": {
        "title": "Directora de Marketing Digital",
        "company_size": "Grande",
        "industry": "E-commerce",
        "responsibilities": ["Estrategia digital", "Gestión de equipos", "ROI optimization"]
      },
      "personal_background": "Profesional experimentada con 12 años en marketing digital, especializada en transformación digital de empresas tradicionales.",
      "goals": ["Digitalizar procesos de marketing", "Mejorar attribution modeling", "Escalar equipos"],
      "challenges": ["Integración de datos", "Presupuesto limitado", "Resistencia al cambio"],
      "buying_process": {
        "research_methods": ["Industry reports", "Peer recommendations", "Demo requests"],
        "decision_factors": ["ROI potential", "Integration capabilities", "Support quality"],
        "timeline": "3-6 meses"
      },
      "objections": ["Costo de implementación", "Curva de aprendizaje", "Dependencia de proveedor"],
      "communication_channels": ["LinkedIn", "Email profesional", "Webinars"],
      "influences": ["Industry analysts", "Peer CMOs", "Technology vendors"],
      "quotes": ["Necesito datos que me permitan tomar decisiones informadas", "La tecnología debe simplificar, no complicar"],
      "typical_day": "Comienza revisando dashboards de performance, tiene reuniones con equipos regionales, evalúa nuevas tecnologías y planifica estrategias trimestrales.",
      "brand_affinities": ["Adobe", "Salesforce", "Google Analytics"],
      "avatar_description": "Mujer profesional ejecutiva, vestimenta business formal, ambiente de oficina moderna",
      "marketing": {
        "content_types": ["Case studies", "ROI calculators", "Industry benchmarks"],
        "messaging_tips": ["Focus on business impact", "Use data-driven arguments", "Highlight integration benefits"],
        "channels": ["LinkedIn", "Industry publications", "Executive events"],
        "content_topics": ["Digital transformation", "Marketing ROI", "Team efficiency"]
      }
    }
  ],
  "insights": [
    "Los decision makers priorizan ROI medible y capacidades de integración",
    "El proceso de evaluación es largo pero meticuloso, involucrando múltiples stakeholders",
    "La confianza en la marca y referencias de peers son factores críticos",
    "Buscan soluciones que escalen con el crecimiento de la empresa",
    "Valoran el soporte técnico y la capacitación del equipo"
  ]
}"""
    
    try:
        # Test parsing
        parsed_data = buyer_persona_service._parse_gemini_response(mock_ai_response)
        print("✅ Response parsing successful")
        
        # Verify structure
        if "personas" in parsed_data and "insights" in parsed_data:
            print("✅ Response structure is correct")
            
            persona = parsed_data["personas"][0]
            required_fields = ["name", "age", "job", "buying_process", "marketing"]
            missing_fields = [field for field in required_fields if field not in persona]
            
            if missing_fields:
                print(f"⚠️  Missing required fields: {missing_fields}")
                return False
            else:
                print("✅ All required persona fields present")
                return True
        else:
            print("❌ Response structure is incorrect")
            return False
            
    except Exception as e:
        print(f"❌ Response parsing failed: {e}")
        return False


async def main():
    """Run all verification tests."""
    print("🚀 Starting Buyer Persona AI Verification Tests\n")
    
    tests = [
        ("Gemini Configuration", test_gemini_configuration),
        ("AI vs Mock Generation", test_ai_vs_mock_generation),
        ("Prompt Quality", test_prompt_quality),
        ("Response Parsing", test_response_parsing),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "="*50)
    print("📊 VERIFICATION SUMMARY")
    print("="*50)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED - Buyer Persona service is using real AI generation!")
    else:
        print(f"\n⚠️  {total - passed} tests failed - There may be issues with AI generation")
        
        if not results.get("Gemini Configuration", False):
            print("\n🔧 RECOMMENDATION: Check GEMINI_API_KEY environment variable")
        
        if not results.get("AI vs Mock Generation", False):
            print("\n🔧 RECOMMENDATION: Verify Gemini AI is actually being called, not falling back to mock data")


if __name__ == "__main__":
    asyncio.run(main())
