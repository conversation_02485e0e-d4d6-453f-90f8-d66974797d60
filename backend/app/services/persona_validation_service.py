"""Validation service for buyer persona data."""

import logging
from typing import Dict, Any

from app.schemas.buyer_persona import (
    BuyerPersona,
    MarketingRecommendation,
    JobInfo,
    BuyingProcess,
    BuyerPersonaRequest
)

logger = logging.getLogger(__name__)


class PersonaValidationService:
    """Service for validating and creating persona objects."""

    def create_validated_persona(self, persona_data: Dict[str, Any], request: BuyerPersonaRequest) -> BuyerPersona:
        """Create and validate a buyer persona from raw data."""
        try:
            # Validate and sanitize persona data
            name = persona_data.get('name', 'Persona Anónima')
            age = persona_data.get('age', 35)

            # Validate age range
            if not isinstance(age, int) or age < 18 or age > 80:
                age = 35

            # Create JobInfo with validation
            job_data = persona_data.get('job', {})
            job_info = JobInfo(
                title=job_data.get('title', 'Profesional'),
                company_size=job_data.get('company_size', 'Mediana'),
                industry=job_data.get('industry', request.industry or 'Tecnología'),
                responsibilities=job_data.get('responsibilities', ['Responsabilidades generales'])
            )

            # Create BuyingProcess
            buying_process = BuyingProcess(
                research_methods=persona_data.get('buying_process', {}).get('research_methods', ['Online research']),
                decision_factors=persona_data.get('buying_process', {}).get('decision_factors', ['Price', 'Quality']),
                timeline=persona_data.get('buying_process', {}).get('timeline', '2-4 weeks')
            )

            # Create BuyerPersona with validation
            persona = BuyerPersona(
                name=name,
                age=age,
                gender=persona_data.get('gender', 'No especificado'),
                location=persona_data.get('location', 'Ciudad'),
                education=persona_data.get('education', 'Universitaria'),
                income_level=persona_data.get('income_level', 'Medio'),
                marital_status=persona_data.get('marital_status', 'Soltero/a'),
                job=job_info,
                personal_background=persona_data.get('personal_background', 'Profesional con experiencia'),
                goals=persona_data.get('goals', ['Crecimiento profesional']),
                challenges=persona_data.get('challenges', ['Gestión del tiempo']),
                buying_process=buying_process,
                objections=persona_data.get('objections', ['Precio elevado']),
                communication_channels=persona_data.get('communication_channels', ['Email', 'LinkedIn']),
                influences=persona_data.get('influences', ['Colegas', 'Expertos del sector']),
                quotes=persona_data.get('quotes', ['Busco soluciones eficientes']),
                typical_day=persona_data.get('typical_day', 'Día ocupado con múltiples responsabilidades'),
                brand_affinities=persona_data.get('brand_affinities', ['Marcas reconocidas']),
                avatar_description=persona_data.get('avatar_description', 'Profesional moderno')
            )

            return persona

        except Exception as e:
            logger.error(f"Error creating validated persona: {e}")
            raise

    def create_marketing_recommendation(self, persona_data: Dict[str, Any], persona_name: str) -> MarketingRecommendation:
        """Create marketing recommendation from persona data."""
        try:
            marketing_rec = MarketingRecommendation(
                persona_name=persona_name,
                content_types=persona_data.get('marketing', {}).get('content_types', ['Blog posts', 'Case studies']),
                messaging_tips=persona_data.get('marketing', {}).get('messaging_tips', ['Enfócate en beneficios']),
                channels=persona_data.get('marketing', {}).get('channels', ['LinkedIn', 'Email']),
                content_topics=persona_data.get('marketing', {}).get('content_topics', ['Productividad', 'Eficiencia'])
            )

            return marketing_rec

        except Exception as e:
            logger.error(f"Error creating marketing recommendation: {e}")
            raise

    def validate_persona_data(self, persona_data: Dict[str, Any]) -> bool:
        """Validate persona data structure."""
        required_fields = ['name', 'age', 'job', 'goals', 'challenges']
        
        for field in required_fields:
            if field not in persona_data:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # Validate job structure
        job_data = persona_data.get('job', {})
        job_required_fields = ['title', 'company_size', 'industry']
        
        for field in job_required_fields:
            if field not in job_data:
                logger.warning(f"Missing required job field: {field}")
                return False
        
        return True

    def sanitize_list_field(self, data: Any, default: list) -> list:
        """Sanitize list fields to ensure they're valid."""
        if not isinstance(data, list):
            return default
        
        # Filter out empty strings and ensure all items are strings
        sanitized = [str(item).strip() for item in data if item and str(item).strip()]
        
        return sanitized if sanitized else default

    def sanitize_string_field(self, data: Any, default: str) -> str:
        """Sanitize string fields to ensure they're valid."""
        if not data or not isinstance(data, str):
            return default
        
        sanitized = str(data).strip()
        return sanitized if sanitized else default

    def validate_age(self, age: Any) -> int:
        """Validate and sanitize age field."""
        try:
            age_int = int(age)
            if 18 <= age_int <= 100:
                return age_int
            else:
                logger.warning(f"Age {age_int} out of valid range, using default")
                return 35
        except (ValueError, TypeError):
            logger.warning(f"Invalid age value: {age}, using default")
            return 35

    def validate_buying_process(self, buying_process_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize buying process data."""
        validated = {
            'research_methods': self.sanitize_list_field(
                buying_process_data.get('research_methods'), 
                ['Online research', 'Peer recommendations']
            ),
            'decision_factors': self.sanitize_list_field(
                buying_process_data.get('decision_factors'), 
                ['Price', 'Quality', 'Support']
            ),
            'timeline': self.sanitize_string_field(
                buying_process_data.get('timeline'), 
                '2-4 weeks'
            )
        }
        
        return validated

    def validate_job_info(self, job_data: Dict[str, Any], fallback_industry: str = None) -> Dict[str, Any]:
        """Validate and sanitize job information."""
        validated = {
            'title': self.sanitize_string_field(job_data.get('title'), 'Profesional'),
            'company_size': self.sanitize_string_field(job_data.get('company_size'), 'Mediana'),
            'industry': self.sanitize_string_field(
                job_data.get('industry'), 
                fallback_industry or 'Tecnología'
            ),
            'responsibilities': self.sanitize_list_field(
                job_data.get('responsibilities'), 
                ['Responsabilidades generales']
            )
        }
        
        return validated
