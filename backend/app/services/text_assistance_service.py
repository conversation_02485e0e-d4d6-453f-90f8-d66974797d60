"""Text assistance service for improving content."""

import logging
from typing import Dict, Any

from app.schemas.buyer_persona import TextAssistRequest, TextAssistResponse
from app.services.ai_provider_service import AIProviderService

logger = logging.getLogger(__name__)


class TextAssistanceService:
    """Service for improving text content using AI assistance."""

    def __init__(self):
        """Initialize the text assistance service."""
        self.ai_provider = AIProviderService()

    async def improve_text(self, request: TextAssistRequest) -> TextAssistResponse:
        """Improve text content using AI assistance."""
        try:
            if self.ai_provider.is_ai_available():
                return await self._improve_text_with_ai(request)
            else:
                return await self._improve_text_mock(request)
        except Exception as e:
            logger.error(f"Error improving text: {e}")
            return TextAssistResponse(
                status="error",
                suggestions=[],
                original_content=request.content,
                error_message=f"Error improving text: {str(e)}"
            )

    async def _improve_text_with_ai(self, request: TextAssistRequest) -> TextAssistResponse:
        """Improve text using AI."""
        prompt = self._create_text_improvement_prompt(request)

        try:
            response = await self.ai_provider.generate_content(prompt)
            parsed_response = self.ai_provider.parse_response(response)

            suggestions = parsed_response.get('suggestions', [])

            return TextAssistResponse(
                status="success",
                suggestions=suggestions,
                original_content=request.content
            )

        except Exception as e:
            logger.error(f"Error in AI text improvement: {e}")
            return await self._improve_text_mock(request)

    async def _improve_text_mock(self, request: TextAssistRequest) -> TextAssistResponse:
        """Provide mock text improvements."""
        suggestions = [
            f"{request.content} - Versión optimizada para mayor claridad y impacto",
            f"Mejora: {request.content} con enfoque en beneficios específicos",
            f"Versión profesional: {request.content} adaptada para audiencia ejecutiva",
            f"Enfoque en resultados: {request.content} con métricas y datos concretos"
        ]

        return TextAssistResponse(
            status="success",
            suggestions=suggestions,
            original_content=request.content
        )

    def _create_text_improvement_prompt(self, request: TextAssistRequest) -> str:
        """Create prompt for text improvement."""
        improvement_type = request.improvement_type or "marketing"
        context = request.context or "Generación de buyer personas"
        target_audience = request.target_audience or "Profesionales de marketing"

        prompt = f"""
Eres un experto en copywriting y marketing. Mejora el siguiente texto para que sea más efectivo.

TEXTO ORIGINAL:
{request.content}

CONTEXTO: {context}
TIPO DE MEJORA: {improvement_type}
AUDIENCIA OBJETIVO: {target_audience}

INSTRUCCIONES:
1. Proporciona 3-5 versiones mejoradas del texto
2. Cada versión debe ser más específica, clara y orientada a resultados
3. Mantén el mensaje principal pero hazlo más impactante
4. Usa un lenguaje profesional pero accesible
5. Adapta el tono según el tipo de mejora solicitado:
   - clarity: Enfócate en claridad y comprensión
   - marketing: Enfócate en persuasión y beneficios
   - professional: Usa tono formal y técnico
   - persuasive: Enfócate en llamadas a la acción

FORMATO DE RESPUESTA (JSON):
{{
  "suggestions": [
    "versión_mejorada_1",
    "versión_mejorada_2",
    "versión_mejorada_3",
    "versión_mejorada_4",
    "versión_mejorada_5"
  ]
}}

IMPORTANTE: Responde SOLO con el JSON válido, sin texto adicional."""

        return prompt
