"""Core persona generation service following SOLID principles."""

import logging
import uuid
import time
from datetime import datetime
from typing import Dict, Any

from app.schemas.buyer_persona import (
    BuyerPersonaRequest,
    BuyerPersonaResponse,
    BuyerPersona,
    MarketingRecommendation,
    JobInfo,
    BuyingProcess
)
from app.services.ai_provider_service import AIProviderService
from app.services.persona_validation_service import PersonaValidationService
from app.services.avatar_service import avatar_service

logger = logging.getLogger(__name__)


class PersonaGenerationService:
    """Service responsible for generating buyer personas."""

    def __init__(self):
        """Initialize the persona generation service."""
        self.ai_provider = AIProviderService()
        self.validator = PersonaValidationService()

    async def generate_buyer_personas(self, request: BuyerPersonaRequest) -> BuyerPersonaResponse:
        """Generate buyer personas based on the request."""
        request_id = str(uuid.uuid4())
        timestamp = datetime.now().isoformat()
        start_time = time.time()

        try:
            # Validate and build product description
            product_description = self._build_product_description(request)

            logger.info(f"Generating {request.num_personas} buyer personas for request {request_id}")

            # Generate personas using AI
            response = await self._generate_with_ai(request, product_description, request_id, timestamp)

            # Add generation metadata
            generation_time = time.time() - start_time
            response.generation_time_seconds = generation_time
            response.ai_model_used = self.ai_provider.get_model_name()

            logger.info(f"Generated {len(response.buyer_personas)} personas in {generation_time:.2f}s for request {request_id}")
            return response

        except Exception as e:
            logger.error(f"Error generating buyer personas for request {request_id}: {e}", exc_info=True)
            return self._create_error_response(request_id, timestamp, str(e), time.time() - start_time)

    def _build_product_description(self, request: BuyerPersonaRequest) -> str:
        """Build comprehensive product description from request data."""
        if request.smart_form_data:
            return self._build_from_smart_form(request.smart_form_data)
        elif request.product_description:
            return self._enhance_basic_description(request)
        else:
            raise ValueError("Either smart_form_data or product_description must be provided")

    def _build_from_smart_form(self, form_data) -> str:
        """Build detailed description from smart form data."""
        # Map enum values to human-readable text
        product_type_map = {
            "physical": "Producto Físico",
            "software": "Software/SaaS",
            "service": "Servicio",
            "course": "Curso/Educación",
            "content": "Contenido Digital"
        }

        price_range_map = {
            "free": "Gratis ($0)",
            "low": "Económico ($1 - $50)",
            "medium": "Medio ($51 - $500)",
            "high": "Premium ($501 - $2,000)",
            "enterprise": "Enterprise ($2,000+)"
        }

        geographic_scope_map = {
            "local": "Local (ciudad/región)",
            "national": "Nacional",
            "international": "Internacional",
            "online": "Online Global"
        }

        urgency_map = {
            "critical": "Crítico - Lo necesitan YA",
            "important": "Importante - Pueden esperar",
            "nice": "Nice-to-have",
            "future": "Prevención/Mejora futura"
        }

        audience_knowledge_map = {
            "clear": "Tengo claro mi público",
            "general": "Tengo una idea general",
            "unsure": "No estoy seguro",
            "explore": "Quiero explorar nuevos segmentos"
        }

        description = f"""
INFORMACIÓN DETALLADA DEL PRODUCTO/SERVICIO:
• Producto: {form_data.product_name}
• Tipo: {product_type_map.get(form_data.product_type, form_data.product_type)}
• Industria: {form_data.industry}
• Rango de precio: {price_range_map.get(form_data.price_range, form_data.price_range)}
• Propuesta de valor única: {form_data.unique_value or 'No especificada'}

AUDIENCIA Y MERCADO:
• Audiencia objetivo: {form_data.target_audience}
• Conocimiento del público: {audience_knowledge_map.get(form_data.audience_knowledge, form_data.audience_knowledge)}
• Público actual: {form_data.existing_audience or 'No especificado'}
• Tipos de cliente objetivo: {', '.join(form_data.business_sizes)}

PROBLEMA Y CONTEXTO:
• Problema principal que resuelve: {form_data.main_problem}
• Nivel de urgencia: {urgency_map.get(form_data.problem_urgency, form_data.problem_urgency)}
• Decisor de compra: {form_data.decision_maker}

GEOGRAFÍA Y DISTRIBUCIÓN:
• Alcance geográfico: {geographic_scope_map.get(form_data.geographic_scope, form_data.geographic_scope)}
• País objetivo principal: {form_data.target_country}
• Regiones específicas: {', '.join(form_data.target_regions) if form_data.target_regions else 'No especificadas'}
• Canales de venta: {', '.join(form_data.sales_channels)}

CONTEXTO CULTURAL Y GEOGRÁFICO:
• Mercado principal: {form_data.target_country}
• Enfoque: Adaptar personas al contexto cultural y socioeconómico de {form_data.target_country}
• Considerar características locales, preferencias culturales y comportamientos de compra específicos de la región
        """.strip()

        return description

    def _enhance_basic_description(self, request: BuyerPersonaRequest) -> str:
        """Enhance basic product description with additional context."""
        description = request.product_description

        if request.industry:
            description += f"\n\nIndustria: {request.industry}"
        if request.target_market:
            description += f"\nMercado objetivo: {request.target_market}"
        if request.business_goals:
            description += f"\nObjetivos de negocio: {request.business_goals}"
        if request.competitors:
            description += f"\nCompetidores principales: {request.competitors}"

        # Add target countries context
        if request.target_countries and len(request.target_countries) > 0:
            countries_text = ", ".join(request.target_countries)
            description += f"\n\nPAÍSES OBJETIVO: {countries_text}"
            description += f"\nCONTEXTO CULTURAL: Adaptar buyer personas al contexto cultural, socioeconómico y de comportamiento específico de: {countries_text}"
            description += f"\nConsiderar características locales, preferencias culturales, idioma, moneda y patrones de compra específicos de estas regiones."

        return description

    async def _generate_with_ai(self, request: BuyerPersonaRequest, product_description: str, request_id: str, timestamp: str) -> BuyerPersonaResponse:
        """Generate buyer personas using AI provider."""
        try:
            # Create comprehensive prompt
            prompt = self._create_enhanced_persona_prompt(product_description, request)

            # Generate content with AI
            response = await self.ai_provider.generate_content(prompt)

            # Parse and validate the response
            personas_data = self.ai_provider.parse_response(response)

            # Convert to Pydantic models and generate avatars
            buyer_personas = []
            marketing_recommendations = []

            for persona_data in personas_data.get('personas', []):
                try:
                    # Validate and create persona
                    persona = self.validator.create_validated_persona(persona_data, request)

                    # Generate unique avatar for this persona using Stability AI
                    try:
                        avatar_result = await self._generate_persona_avatar(persona, persona_data)
                        if avatar_result and avatar_result.get("status") == "success":
                            # Add avatar data to persona (we'll extend the schema if needed)
                            persona.avatar_url = avatar_result.get("avatar_url")
                            persona.avatar_id = avatar_result.get("avatar_id")
                            logger.info(f"Generated unique avatar for persona: {persona.name}")
                        else:
                            logger.warning(f"Avatar generation failed for persona: {persona.name}")
                    except Exception as avatar_error:
                        logger.error(f"Error generating avatar for {persona.name}: {avatar_error}")
                        # Continue without avatar - don't fail the entire persona generation

                    buyer_personas.append(persona)

                    # Create marketing recommendation
                    marketing_rec = self.validator.create_marketing_recommendation(persona_data, persona.name)
                    marketing_recommendations.append(marketing_rec)

                except Exception as e:
                    logger.error(f"Error processing persona data: {e}")
                    continue

            # Generate general insights
            general_insights = personas_data.get('insights', [
                "Los buyer personas muestran una clara preferencia por soluciones eficientes",
                "La comunicación digital es el canal preferido para este segmento",
                "El precio y la calidad son factores decisivos en el proceso de compra"
            ])

            return BuyerPersonaResponse(
                status="success",
                buyer_personas=buyer_personas,
                marketing_recommendations=marketing_recommendations,
                general_insights=general_insights,
                timestamp=timestamp,
                request_id=request_id
            )

        except Exception as e:
            logger.error(f"Error in AI generation: {e}")
            raise

    def _create_enhanced_persona_prompt(self, product_description: str, request: BuyerPersonaRequest) -> str:
        """Create an enhanced comprehensive prompt for AI."""
        # Add unique identifiers to ensure diverse generation
        unique_seed = f"{request.request_timestamp}_{hash(product_description) % 10000}" if hasattr(request, 'request_timestamp') else f"{int(time.time() * 1000) % 10000}"

        # Build geographic context
        geographic_context = ""
        if request.target_countries and len(request.target_countries) > 0:
            countries_list = ", ".join(request.target_countries)
            geographic_context = f"""
CONTEXTO GEOGRÁFICO ESPECÍFICO:
• Países objetivo: {countries_list}
• IMPORTANTE: Adaptar cada buyer persona al contexto cultural, socioeconómico y de comportamiento de estos países
• Considerar características locales: idioma, moneda, patrones de compra, preferencias culturales
• Incluir nombres, ubicaciones y referencias culturales apropiadas para estas regiones
• Ajustar niveles de ingresos y poder adquisitivo según la economía local
"""

        prompt = f"""
Eres un experto en marketing y análisis de buyer personas con 15+ años de experiencia. Genera {request.num_personas} buyer personas COMPLETAMENTE ÚNICOS Y DIVERSOS basados en la siguiente información:

INFORMACIÓN COMPLETA DEL PRODUCTO/SERVICIO:
{product_description}
{geographic_context}
CONTEXTO ADICIONAL:
• Objetivos de negocio: {request.business_goals or 'Crecimiento y adquisición de clientes'}
• Competidores principales: {request.competitors or 'Competencia general del sector'}
• Número de personas solicitadas: {request.num_personas}
• ID único de generación: {unique_seed}

INSTRUCCIONES CRÍTICAS PARA DIVERSIDAD:
1. Crea {request.num_personas} buyer personas COMPLETAMENTE DIFERENTES entre sí
2. Varía SIGNIFICATIVAMENTE: edad (20-65), género, ubicación geográfica, industria, nivel socioeconómico
3. Cada persona debe tener personalidades, motivaciones y comportamientos ÚNICOS
4. Diversifica roles profesionales: desde junior hasta C-level, diferentes sectores
5. Incluye diferentes estilos de comunicación y canales preferidos
6. Varía los procesos de compra y factores de decisión
7. Asegúrate de que NO haya similitudes entre las personas generadas

FORMATO DE RESPUESTA (JSON):
{{
  "personas": [
    {{
      "name": "Nombre completo realista",
      "age": número_edad,
      "gender": "Masculino/Femenino/No binario",
      "location": "Ciudad, País",
      "education": "Nivel educativo",
      "income_level": "Bajo/Medio-bajo/Medio/Medio-alto/Alto",
      "marital_status": "Estado civil",
      "job": {{
        "title": "Título del trabajo",
        "company_size": "Startup/Pequeña/Mediana/Grande/Corporativa",
        "industry": "Industria específica",
        "responsibilities": ["responsabilidad1", "responsabilidad2", "responsabilidad3"]
      }},
      "personal_background": "Historia personal detallada (2-3 oraciones)",
      "goals": ["objetivo1", "objetivo2", "objetivo3"],
      "challenges": ["desafío1", "desafío2", "desafío3"],
      "buying_process": {{
        "research_methods": ["método1", "método2", "método3"],
        "decision_factors": ["factor1", "factor2", "factor3"],
        "timeline": "tiempo_decisión"
      }},
      "objections": ["objeción1", "objeción2", "objeción3"],
      "communication_channels": ["canal1", "canal2", "canal3"],
      "influences": ["influencia1", "influencia2", "influencia3"],
      "quotes": ["frase_típica1", "frase_típica2", "frase_típica3"],
      "typical_day": "Descripción detallada de un día típico",
      "brand_affinities": ["marca1", "marca2", "marca3"],
      "avatar_description": "Descripción física y de estilo",
      "marketing": {{
        "content_types": ["tipo1", "tipo2", "tipo3"],
        "messaging_tips": ["tip1", "tip2", "tip3"],
        "channels": ["canal1", "canal2", "canal3"],
        "content_topics": ["tema1", "tema2", "tema3"]
      }}
    }}
  ],
  "insights": [
    "insight_general_1",
    "insight_general_2",
    "insight_general_3",
    "insight_general_4",
    "insight_general_5"
  ]
}}

CONFIGURACIÓN DE GENERACIÓN:
• Usa alta creatividad y diversidad (temperatura elevada)
• Cada buyer_persona debe ser completamente único
• Basa las respuestas en datos realistas y específicos del mercado objetivo
• Evita respuestas genéricas o plantillas predefinidas

IMPORTANTE: Responde SOLO con el JSON válido, sin texto adicional."""

        return prompt

    async def _generate_persona_avatar(self, persona: BuyerPersona, persona_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a unique avatar for the persona using Stability AI."""
        try:
            # Extract avatar characteristics from persona data
            gender = self._normalize_gender(persona.gender)
            age = persona.age
            ethnicity = self._extract_ethnicity_from_location(persona.location)

            # Create comprehensive persona description for avatar generation
            avatar_description = f"""
            {persona.personal_background}

            Professional: {persona.job.title} in {persona.job.industry}
            Age: {age} years old
            Location: {persona.location}
            Education: {persona.education}
            Income: {persona.income_level}

            Personality traits: {', '.join(persona.goals[:2])}
            """

            # Generate avatar using the avatar service
            avatar_result = await avatar_service.generate_avatar(
                persona_description=avatar_description,
                style="professional",  # Always use professional style for buyer personas
                gender=gender,
                age=age,
                ethnicity=ethnicity
            )

            return avatar_result

        except Exception as e:
            logger.error(f"Error generating avatar for persona {persona.name}: {e}")
            return {"status": "error", "error_message": str(e)}

    def _normalize_gender(self, gender: str) -> str:
        """Normalize gender for avatar generation."""
        gender_lower = gender.lower()
        if "masculino" in gender_lower or "male" in gender_lower or "hombre" in gender_lower:
            return "male"
        elif "femenino" in gender_lower or "female" in gender_lower or "mujer" in gender_lower:
            return "female"
        else:
            return "neutral"

    def _extract_ethnicity_from_location(self, location: str) -> str:
        """Extract likely ethnicity from location for avatar generation."""
        location_lower = location.lower()

        # Spanish/Latin American locations
        hispanic_countries = [
            "españa", "spain", "méxico", "mexico", "argentina", "colombia", "chile",
            "perú", "peru", "venezuela", "ecuador", "uruguay", "paraguay", "bolivia",
            "guatemala", "costa rica", "panamá", "panama", "república dominicana",
            "dominican republic", "cuba", "honduras", "nicaragua", "el salvador"
        ]
        if any(country in location_lower for country in hispanic_countries):
            return "hispanic"

        # European locations
        european_countries = [
            "francia", "france", "alemania", "germany", "italia", "italy",
            "reino unido", "uk", "united kingdom", "holanda", "netherlands",
            "países bajos", "bélgica", "belgium", "suiza", "switzerland",
            "austria", "suecia", "sweden", "noruega", "norway", "dinamarca",
            "denmark", "finlandia", "finland", "polonia", "poland", "república checa",
            "czech republic", "hungría", "hungary", "grecia", "greece", "irlanda",
            "ireland", "portugal", "rumania", "romania", "bulgaria", "rusia", "russia"
        ]
        if any(country in location_lower for country in european_countries):
            return "caucasian"

        # Asian locations
        asian_countries = [
            "china", "japón", "japan", "corea", "korea", "india", "tailandia",
            "thailand", "malasia", "malaysia", "singapur", "singapore", "indonesia",
            "filipinas", "philippines", "vietnam", "viet nam"
        ]
        if any(country in location_lower for country in asian_countries):
            return "asian"

        # Middle Eastern locations
        middle_eastern_countries = [
            "emiratos árabes unidos", "uae", "arabia saudí", "saudi arabia",
            "israel", "turquía", "turkey"
        ]
        if any(country in location_lower for country in middle_eastern_countries):
            return "middle_eastern"

        # African locations
        african_countries = [
            "sudáfrica", "south africa", "egipto", "egypt", "nigeria", "kenia", "kenya"
        ]
        if any(country in location_lower for country in african_countries):
            return "african"

        # North American locations (non-Hispanic)
        if any(country in location_lower for country in ["estados unidos", "united states", "usa", "canadá", "canada"]):
            return "diverse"  # Mixed ethnicity for North America

        # Brazilian (Portuguese-speaking)
        if any(country in location_lower for country in ["brasil", "brazil"]):
            return "hispanic"  # Similar appearance to Hispanic

        # Default to diverse for unknown locations
        return "diverse"

    def _create_error_response(self, request_id: str, timestamp: str, error_message: str, generation_time: float) -> BuyerPersonaResponse:
        """Create error response."""
        return BuyerPersonaResponse(
            status="error",
            buyer_personas=[],
            marketing_recommendations=[],
            general_insights=[],
            timestamp=timestamp,
            request_id=request_id,
            error_message=f"Internal error: {error_message}",
            generation_time_seconds=generation_time
        )
