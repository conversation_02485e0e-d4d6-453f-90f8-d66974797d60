#!/bin/bash

# Emma Studio Development Environment Setup Script
# This script automates the complete setup process for new developers

set -e  # Exit on any error

# Color codes for better output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Emma Studio"
REQUIRED_NODE_VERSION="18"
REQUIRED_PYTHON_VERSION="3.10"
BACKEND_PORT="8000"
FRONTEND_PORT="5173"

# Print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if port is available
is_port_available() {
    ! lsof -i :$1 >/dev/null 2>&1
}

# Detect operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        echo "linux"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        echo "macos"
    elif [[ "$OSTYPE" == "cygwin" ]] || [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
        echo "windows"
    else
        echo "unknown"
    fi
}

# Check system requirements
check_system_requirements() {
    print_header "Checking System Requirements"
    
    local os=$(detect_os)
    print_status "Detected OS: $os"
    
    # Check Node.js
    if command_exists node; then
        local node_version=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
        if [ "$node_version" -ge "$REQUIRED_NODE_VERSION" ]; then
            print_success "Node.js $(node --version) is installed"
        else
            print_error "Node.js version $node_version is too old. Required: $REQUIRED_NODE_VERSION+"
            return 1
        fi
    else
        print_error "Node.js is not installed"
        return 1
    fi
    
    # Check npm
    if command_exists npm; then
        print_success "npm $(npm --version) is installed"
    else
        print_error "npm is not installed"
        return 1
    fi
    
    # Check Python
    if command_exists python3; then
        local python_version=$(python3 --version | cut -d' ' -f2 | cut -d'.' -f1,2)
        if [[ "$python_version" == "3.10"* ]] || [[ "$python_version" == "3.11"* ]] || [[ "$python_version" == "3.12"* ]]; then
            print_success "Python $(python3 --version) is installed"
        else
            print_error "Python version $python_version is not supported. Required: 3.10, 3.11, or 3.12"
            return 1
        fi
    else
        print_error "Python 3 is not installed"
        return 1
    fi
    
    # Check Poetry
    if command_exists poetry; then
        print_success "Poetry $(poetry --version | cut -d' ' -f3) is installed"
    else
        print_error "Poetry is not installed"
        return 1
    fi
    
    # Check Docker
    if command_exists docker; then
        if docker info >/dev/null 2>&1; then
            print_success "Docker is installed and running"
        else
            print_warning "Docker is installed but not running"
        fi
    else
        print_warning "Docker is not installed (optional for Qdrant)"
    fi
    
    # Check Git
    if command_exists git; then
        print_success "Git is installed"
    else
        print_error "Git is not installed"
        return 1
    fi
    
    return 0
}

# Install missing prerequisites
install_prerequisites() {
    print_header "Installing Missing Prerequisites"
    
    local os=$(detect_os)
    
    case $os in
        "macos")
            if ! command_exists brew; then
                print_status "Installing Homebrew..."
                /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
            fi
            
            if ! command_exists node; then
                print_status "Installing Node.js..."
                brew install node
            fi
            
            if ! command_exists python3; then
                print_status "Installing Python..."
                brew install python@3.11
            fi
            
            if ! command_exists poetry; then
                print_status "Installing Poetry..."
                curl -sSL https://install.python-poetry.org | python3 -
                export PATH="$HOME/.local/bin:$PATH"
            fi
            
            if ! command_exists docker; then
                print_warning "Please install Docker Desktop from https://www.docker.com/products/docker-desktop"
            fi
            ;;
        "linux")
            # Update package manager
            if command_exists apt-get; then
                sudo apt-get update
                
                if ! command_exists node; then
                    print_status "Installing Node.js..."
                    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
                    sudo apt-get install -y nodejs
                fi
                
                if ! command_exists python3; then
                    print_status "Installing Python..."
                    sudo apt-get install -y python3 python3-pip python3-venv
                fi
                
                if ! command_exists poetry; then
                    print_status "Installing Poetry..."
                    curl -sSL https://install.python-poetry.org | python3 -
                    export PATH="$HOME/.local/bin:$PATH"
                fi
                
                if ! command_exists docker; then
                    print_status "Installing Docker..."
                    curl -fsSL https://get.docker.com -o get-docker.sh
                    sudo sh get-docker.sh
                    sudo usermod -aG docker $USER
                    print_warning "Please log out and back in for Docker permissions to take effect"
                fi
            elif command_exists yum; then
                # RedHat/CentOS/Fedora
                if ! command_exists node; then
                    print_status "Installing Node.js..."
                    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
                    sudo yum install -y nodejs
                fi
                
                if ! command_exists python3; then
                    print_status "Installing Python..."
                    sudo yum install -y python3 python3-pip
                fi
                
                if ! command_exists poetry; then
                    print_status "Installing Poetry..."
                    curl -sSL https://install.python-poetry.org | python3 -
                    export PATH="$HOME/.local/bin:$PATH"
                fi
            fi
            ;;
        "windows")
            print_warning "Windows detected. Please install prerequisites manually:"
            print_warning "1. Node.js 18+ from https://nodejs.org/"
            print_warning "2. Python 3.10+ from https://python.org/"
            print_warning "3. Poetry from https://python-poetry.org/docs/#installation"
            print_warning "4. Docker Desktop from https://www.docker.com/products/docker-desktop"
            print_warning "5. Git from https://git-scm.com/"
            ;;
    esac
}

# Setup environment files
setup_environment() {
    print_header "Setting Up Environment Configuration"
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend/.env from template..."
        cp backend/.env.example backend/.env
        print_warning "Please edit backend/.env with your API keys"
    else
        print_success "Backend .env file already exists"
    fi
    
    # Root environment (for frontend)
    if [ ! -f ".env" ]; then
        print_status "Creating .env from template..."
        cp .env.example .env
        print_warning "Please edit .env with your API keys"
    else
        print_success "Root .env file already exists"
    fi
    
    print_status "Environment files created. You'll need to add your API keys:"
    echo "  - GEMINI_API_KEY (Google AI Studio)"
    echo "  - STABILITY_API_KEY (Stability AI)"
    echo "  - OPENAI_API_KEY (OpenAI)"
    echo "  - ELEVENLABS_API_KEY (ElevenLabs)"
    echo "  - GOOGLE_API_KEY (Google Cloud)"
    echo "  - SERPER_API_KEY (Serper.dev)"
    echo "  - VITE_POLOTNO_API_KEY (Polotno Studio)"
    echo "  - Database configuration for Supabase"
}

# Install backend dependencies
install_backend_dependencies() {
    print_header "Installing Backend Dependencies"

    if [ ! -d "backend" ]; then
        print_error "Backend directory not found. Are you in the project root?"
        return 1
    fi

    cd backend

    print_status "Installing Python dependencies with Poetry..."
    poetry install

    print_success "Backend dependencies installed successfully"
    cd ..
}

# Install frontend dependencies
install_frontend_dependencies() {
    print_header "Installing Frontend Dependencies"

    if [ ! -d "client" ]; then
        print_error "Client directory not found. Are you in the project root?"
        return 1
    fi

    cd client

    print_status "Installing Node.js dependencies..."
    npm install

    print_success "Frontend dependencies installed successfully"
    cd ..
}

# Setup database
setup_database() {
    print_header "Setting Up Database"

    print_status "Database setup requires Supabase configuration"
    print_status "Please follow these steps:"
    echo "1. Create a Supabase project at https://supabase.com"
    echo "2. Copy your project URL and anon key"
    echo "3. Update DATABASE_URL in your .env files"
    echo "4. Run the SQL schema from supabase-schema.sql in your Supabase SQL editor"

    if [ -f "supabase-schema.sql" ]; then
        print_success "Database schema file found: supabase-schema.sql"
    else
        print_warning "Database schema file not found"
    fi
}

# Setup Docker services
setup_docker_services() {
    print_header "Setting Up Docker Services"

    if ! command_exists docker; then
        print_warning "Docker not found. Skipping Docker services setup."
        return 0
    fi

    if ! docker info >/dev/null 2>&1; then
        print_warning "Docker is not running. Please start Docker and run this script again."
        return 0
    fi

    print_status "Setting up Qdrant vector database..."

    # Check if Qdrant container already exists
    if docker ps -a | grep -q qdrant; then
        print_status "Qdrant container already exists. Starting it..."
        docker start qdrant || true
    else
        print_status "Creating new Qdrant container..."
        docker run -d --name qdrant -p 6333:6333 qdrant/qdrant
    fi

    # Wait for Qdrant to be ready
    print_status "Waiting for Qdrant to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:6333/health >/dev/null 2>&1; then
            print_success "Qdrant is running on port 6333"
            break
        fi
        sleep 1
    done
}

# Verify installation
verify_installation() {
    print_header "Verifying Installation"

    local errors=0

    # Check backend
    print_status "Checking backend setup..."
    if [ -d "backend" ] && [ -f "backend/pyproject.toml" ]; then
        cd backend
        if poetry check >/dev/null 2>&1; then
            print_success "Backend Poetry configuration is valid"
        else
            print_error "Backend Poetry configuration has issues"
            errors=$((errors + 1))
        fi
        cd ..
    else
        print_error "Backend setup incomplete"
        errors=$((errors + 1))
    fi

    # Check frontend
    print_status "Checking frontend setup..."
    if [ -d "client" ] && [ -f "client/package.json" ]; then
        if [ -d "client/node_modules" ]; then
            print_success "Frontend dependencies installed"
        else
            print_error "Frontend dependencies not installed"
            errors=$((errors + 1))
        fi
    else
        print_error "Frontend setup incomplete"
        errors=$((errors + 1))
    fi

    # Check environment files
    print_status "Checking environment configuration..."
    if [ -f ".env" ] && [ -f "backend/.env" ]; then
        print_success "Environment files exist"
    else
        print_error "Environment files missing"
        errors=$((errors + 1))
    fi

    # Check ports
    print_status "Checking port availability..."
    if is_port_available $BACKEND_PORT; then
        print_success "Backend port $BACKEND_PORT is available"
    else
        print_warning "Backend port $BACKEND_PORT is in use"
    fi

    if is_port_available $FRONTEND_PORT; then
        print_success "Frontend port $FRONTEND_PORT is available"
    else
        print_warning "Frontend port $FRONTEND_PORT is in use"
    fi

    return $errors
}

# Show next steps
show_next_steps() {
    print_header "Setup Complete! Next Steps"

    echo -e "${GREEN}🎉 Emma Studio development environment setup is complete!${NC}"
    echo ""
    echo "Next steps:"
    echo "1. Configure your API keys in .env and backend/.env files"
    echo "2. Set up your Supabase database using supabase-schema.sql"
    echo "3. Start the development servers:"
    echo ""
    echo -e "${CYAN}   # Start backend (in one terminal):${NC}"
    echo "   cd backend && poetry run uvicorn app.main:app --reload --port 8000"
    echo ""
    echo -e "${CYAN}   # Start frontend (in another terminal):${NC}"
    echo "   cd client && npm run dev"
    echo ""
    echo -e "${CYAN}   # Or start both with:${NC}"
    echo "   npm run dev"
    echo ""
    echo "4. Open your browser to http://localhost:5173"
    echo ""
    echo "For detailed documentation, see docs/DEVELOPER_SETUP.md"
    echo ""
    echo -e "${YELLOW}Important:${NC} Make sure to configure all required API keys before starting the application."
}

# Main setup function
main() {
    print_header "Emma Studio Development Environment Setup"
    echo -e "${CYAN}Welcome to $PROJECT_NAME development setup!${NC}"
    echo "This script will set up your complete development environment."
    echo ""

    # Check if we're in the right directory
    if [ ! -f "package.json" ] || [ ! -d "backend" ] || [ ! -d "client" ]; then
        print_error "This doesn't appear to be the Emma Studio project root directory."
        print_error "Please run this script from the project root where package.json exists."
        exit 1
    fi

    # Ask for confirmation
    read -p "Do you want to continue with the setup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_status "Setup cancelled by user."
        exit 0
    fi

    # Run setup steps
    if ! check_system_requirements; then
        print_error "System requirements not met. Please install missing prerequisites."
        print_status "You can run this script with --install-prereqs to attempt automatic installation."
        exit 1
    fi

    setup_environment
    install_backend_dependencies
    install_frontend_dependencies
    setup_database
    setup_docker_services

    if verify_installation; then
        show_next_steps
    else
        print_error "Setup completed with some issues. Please check the errors above."
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    --install-prereqs)
        install_prerequisites
        ;;
    --help|-h)
        echo "Emma Studio Development Environment Setup"
        echo ""
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  --install-prereqs    Attempt to install missing prerequisites"
        echo "  --help, -h          Show this help message"
        echo ""
        echo "This script will:"
        echo "  1. Check system requirements"
        echo "  2. Set up environment files"
        echo "  3. Install backend dependencies with Poetry"
        echo "  4. Install frontend dependencies with npm"
        echo "  5. Set up Docker services (Qdrant)"
        echo "  6. Verify the installation"
        echo ""
        echo "Prerequisites:"
        echo "  - Node.js 18+"
        echo "  - Python 3.10+"
        echo "  - Poetry"
        echo "  - Git"
        echo "  - Docker (optional)"
        ;;
    *)
        main
        ;;
esac
