"""
Endpoint para obtener el progreso de un análisis SEO en tiempo real
"""

from fastapi import APIRouter, HTTPException, Path
from typing import Dict, Any, Optional
import time
import json
import os
from datetime import datetime

router = APIRouter()

# Simulación de almacenamiento en memoria para el progreso
# En producción, esto debería estar en Redis o una base de datos
progress_storage: Dict[str, Dict[str, Any]] = {}

@router.get("/progress/{analysis_id}")
async def get_analysis_progress(
    analysis_id: str = Path(..., description="ID del análisis SEO")
) -> Dict[str, Any]:
    """
    Obtiene el progreso actual de un análisis SEO
    """
    try:
        # Verificar si existe el análisis
        if analysis_id not in progress_storage:
            raise HTTPException(
                status_code=404,
                detail=f"Análisis con ID {analysis_id} no encontrado"
            )
        
        progress_data = progress_storage[analysis_id]
        
        # Simular progreso automático si está en curso
        if progress_data.get("phase") != "complete":
            progress_data = simulate_progress_update(analysis_id, progress_data)
        
        return {
            "status": "success",
            "analysis_id": analysis_id,
            "progress": progress_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error al obtener progreso: {str(e)}"
        )

def simulate_progress_update(analysis_id: str, current_progress: Dict[str, Any]) -> Dict[str, Any]:
    """
    Simula la actualización automática del progreso
    """
    now = time.time()
    start_time = current_progress.get("start_time", now)
    elapsed = now - start_time
    
    # Simular diferentes fases basadas en el tiempo transcurrido
    if elapsed < 10:  # Primeros 10 segundos - descubrimiento
        current_progress.update({
            "phase": "discovery",
            "status": "Descubriendo páginas del sitio web...",
            "current_page": 0,
            "total_pages": 0
        })
    elif elapsed < 20:  # Siguientes 10 segundos - establecer total
        if current_progress.get("total_pages", 0) == 0:
            import random
            total_pages = random.randint(20, 100)
            current_progress.update({
                "total_pages": total_pages,
                "status": f"Encontradas {total_pages} páginas. Iniciando análisis...",
                "phase": "analysis"
            })
    else:  # Análisis en progreso
        total_pages = current_progress.get("total_pages", 50)
        current_page = current_progress.get("current_page", 0)
        
        if current_page < total_pages:
            # Incrementar página actual
            current_page = min(current_page + 1, total_pages)
            current_url = f"https://ejemplo.com/pagina-{current_page}"
            
            # Simular errores ocasionales
            import random
            if random.random() < 0.05:  # 5% de probabilidad de error
                current_progress["failed_urls"] = current_progress.get("failed_urls", [])
                current_progress["failed_urls"].append(current_url)
            else:
                current_progress["processed_urls"] = current_progress.get("processed_urls", [])
                current_progress["processed_urls"].append(current_url)
                # Mantener solo las últimas 5 URLs procesadas
                current_progress["processed_urls"] = current_progress["processed_urls"][-5:]
            
            # Calcular tiempo estimado restante
            if current_page > 0:
                rate = current_page / elapsed
                remaining_pages = total_pages - current_page
                estimated_remaining = remaining_pages / rate if rate > 0 else 0
            else:
                estimated_remaining = 0
            
            current_progress.update({
                "current_page": current_page,
                "current_url": current_url,
                "status": f"Analizando página {current_page}/{total_pages}",
                "phase": "analysis",
                "estimated_time_remaining": int(estimated_remaining)
            })
            
        elif current_page >= total_pages and elapsed > 60:  # Después de 1 minuto, completar
            current_progress.update({
                "phase": "recommendations",
                "status": "Generando recomendaciones finales...",
            })
            
            # Completar después de 5 segundos más
            if elapsed > 65:
                current_progress.update({
                    "phase": "complete",
                    "status": "Análisis completado",
                    "completed_at": now
                })
    
    # Actualizar en almacenamiento
    progress_storage[analysis_id] = current_progress
    
    return current_progress

@router.post("/start-progress/{analysis_id}")
async def start_analysis_progress(
    analysis_id: str = Path(..., description="ID del análisis SEO"),
    analysis_data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Inicia el seguimiento de progreso para un análisis SEO
    """
    try:
        start_time = time.time()
        
        initial_progress = {
            "current_page": 0,
            "total_pages": 0,
            "status": "Iniciando análisis...",
            "processed_urls": [],
            "failed_urls": [],
            "phase": "discovery",
            "start_time": start_time,
            "analysis_data": analysis_data or {}
        }
        
        # Almacenar progreso inicial
        progress_storage[analysis_id] = initial_progress
        
        return {
            "status": "success",
            "analysis_id": analysis_id,
            "message": "Seguimiento de progreso iniciado",
            "progress": initial_progress
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error al iniciar seguimiento: {str(e)}"
        )

@router.delete("/progress/{analysis_id}")
async def cleanup_analysis_progress(
    analysis_id: str = Path(..., description="ID del análisis SEO")
) -> Dict[str, Any]:
    """
    Limpia los datos de progreso de un análisis completado
    """
    try:
        if analysis_id in progress_storage:
            del progress_storage[analysis_id]
        
        return {
            "status": "success",
            "message": f"Progreso del análisis {analysis_id} eliminado"
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error al limpiar progreso: {str(e)}"
        )

# Función de limpieza automática (llamar periódicamente)
def cleanup_old_progress():
    """
    Limpia automáticamente el progreso de análisis antiguos
    """
    current_time = time.time()
    to_remove = []
    
    for analysis_id, progress_data in progress_storage.items():
        start_time = progress_data.get("start_time", current_time)
        # Eliminar análisis de más de 1 hora
        if current_time - start_time > 3600:
            to_remove.append(analysis_id)
    
    for analysis_id in to_remove:
        del progress_storage[analysis_id]
    
    return len(to_remove)
